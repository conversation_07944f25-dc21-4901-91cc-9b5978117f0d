.clients-section {
  min-height: 100vh;
  background: #ffffff;
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.clients-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.clients-container {
  max-width: 1200px;
  width: 100%;
  text-align: center;
}

/* Header */
.clients-header {
  margin-bottom: 60px;
}

.clients-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.title-underline {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #00E5FF 0%, #ADFF4D 100%);
  margin: 0 auto;
  border-radius: 2px;
}

/* Company Logos */
.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
  align-items: center;
}

.company-logo {
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: transform 0.3s ease;
}

.company-logo:hover {
  transform: translateY(-5px);
}

/* Star Divider */
.star-divider {
  margin: 60px 0;
  display: flex;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 1s ease 0.5s forwards;
}

/* Description */
.clients-description {
  max-width: 800px;
  margin: 0 auto 80px;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.8s forwards;
}

.clients-description p {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #555;
}

.clients-description .highlight {
  background: linear-gradient(90deg, #C636FF 0%, #156CFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Approaches */
.approaches-container {
  position: relative;
  margin-top: 100px;
}

.wave-line {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 1s ease 1.2s forwards;
}

.wave-svg {
  width: 100%;
  height: 100px;
}

.approaches-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 60px;
  position: relative;
  z-index: 2;
}

.approach-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 40px 20px;
  text-align: center;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.approach-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.approach-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.approach-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.approach-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .clients-section {
    padding: 60px 20px;
  }

  .clients-title {
    font-size: 2.5rem;
  }

  .companies-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .approaches-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .clients-description p {
    font-size: 1.1rem;
  }
}
