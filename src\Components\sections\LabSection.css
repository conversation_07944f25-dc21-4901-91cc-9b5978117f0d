.lab-section {
  min-height: 100vh;
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.lab-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.lab-container {
  max-width: 1000px;
  width: 100%;
  position: relative;
  z-index: 2;
}

/* Contenido principal centrado */
.lab-main-content {
  text-align: center;
  padding: 0 40px;
}

.lab-icon {
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeInUp 1s ease 0.5s forwards;
  display: flex;
  justify-content: center;
}

.lab-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 30px;
  line-height: 1.2;
  opacity: 0;
  animation: fadeInUp 1s ease 0.8s forwards;
}

.highlight-green {
  background: linear-gradient(90deg, #ADFF4D 0%, #17FFE4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.lab-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 50px;
  font-weight: 400;
  line-height: 1.6;
  opacity: 0;
  animation: fadeInUp 1s ease 1.1s forwards;
}

.lab-subtitle strong {
  color: white;
  font-weight: 600;
}

/* Elementos decorativos */
.lab-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(173, 255, 77, 0.1) 0%, rgba(23, 255, 228, 0.1) 100%);
  animation: float 6s ease-in-out infinite;
}

.decoration-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .lab-section {
    padding: 60px 20px;
  }

  .lab-title {
    font-size: 2.5rem;
  }

  .lab-subtitle {
    font-size: 1.1rem;
  }

  .lab-main-content {
    padding: 0 20px;
  }

  .lab-icon {
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .lab-title {
    font-size: 2rem;
  }

  .lab-subtitle {
    font-size: 1rem;
  }

  .decoration-circle {
    width: 80px !important;
    height: 80px !important;
  }
}
