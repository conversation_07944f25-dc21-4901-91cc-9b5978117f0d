/* Reset CSS para consistencia entre navegadores */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #2d3748;
  overflow-x: hidden;
}

/* Eliminar estilos por defecto de elementos */
h1, h2, h3, h4, h5, h6 {
  font-weight: inherit;
  line-height: 1.2;
}

p {
  margin: 0;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

a {
  text-decoration: none;
  color: inherit;
}

ul, ol {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Selección de texto */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: #2d3748;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.2);
  color: #2d3748;
}
