import { useNavigate, useLocation } from 'react-router-dom';
import gretaLogoVerde from '../../assets/IMG/Greta_logo_verde.png';
import gretaLogoBlanco from '../../assets/IMG/Greta_logo_blanco.png';
import './Header.css';

/**
 * <PERSON>er transparente con logo Greta y botón Empecemos
 * Se mantiene fijo en la parte superior
 * Recibe un prop para determinar qué logo usar
 */
const Header = ({ useWhiteLogo = false }) => {
  const navigate = useNavigate();

  const handleEmpecemosClick = () => {
    navigate('/empecemos');
  };

  const handleLogoClick = () => {
    navigate('/');
  };

  return (
    <header className="header">
      <div className="header-content">
        {/* Logo Greta */}
        <div className="header-logo" onClick={handleLogoClick}>
          <img
            src={useWhiteLogo ? gretaLogoBlanco : gretaLogoVerde}
            alt="Greta"
            className="greta-logo-img"
          />
        </div>

        {/* Botón Empecemos */}
        <button className="header-cta-button" onClick={handleEmpecemosClick}>
          Empecemos!
        </button>
      </div>
    </header>
  );
};

export default Header;
