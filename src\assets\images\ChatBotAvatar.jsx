/**
 * Avatar del ChatBot Greta
 * Placeholder - reemplazar con la imagen real del avatar
 */
const ChatBotAvatar = ({ width = 200, height = 200, className = "" }) => {
  return (
    <div className={`${className} flex items-center justify-center bg-blue-100 rounded-full`} style={{ width, height }}>
      {/* Aquí puedes pegar la imagen real del avatar o usar un SVG */}
      <svg width={width * 0.6} height={height * 0.6} viewBox="0 0 120 120" fill="none">
        <circle cx="60" cy="60" r="50" fill="#156CFF" opacity="0.2"/>
        <circle cx="60" cy="45" r="15" fill="#156CFF"/>
        <path d="M30 85C30 70 42 65 60 65C78 65 90 70 90 85" fill="#156CFF"/>
        <text x="60" y="100" fontSize="8" fill="#156CFF" textAnchor="middle" fontFamily="Arial, sans-serif">
          ChatBot Greta
        </text>
      </svg>
    </div>
  );
};

export default ChatBotAvatar;
