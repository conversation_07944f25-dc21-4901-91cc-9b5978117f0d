import { useState, useEffect } from 'react';
import petrotalLogo from '../../assets/logos/petrotal_logo.png';
import ransaLogo from '../../assets/logos/ransa_logo.png';
import nexaLogo from '../../assets/logos/nexa_logo.png';
import alfaparfLogo from '../../assets/logos/alfaparf_logo.png';
import StarIcon from '../../assets/SVG/StarIcon';
import GearsIcon2 from '../../assets/SVG/GearsIcon2';
import PeopleIcon from '../../assets/SVG/PeopleIcon';
import CubeIcon from '../../assets/SVG/CubeIcon';
import './ClientsSection.css';

/**
 * Sección de clientes y enfoques
 * Muestra los logos de empresas que confían en Greta Labs
 * y los tres enfoques: Personas, Servicios y Productos
 */
const ClientsSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const companies = [
    { name: "PetroTal", logo: petrotalLogo },
    { name: "Ransa", logo: ransaLogo },
    { name: "Nexa", logo: nexaLogo },
    { name: "Alfaparf", logo: alfaparfLogo }
  ];

  const approaches = [
    {
      icon: GearsIcon2,
      title: "Servicios",
      description: "Soluciones tecnológicas especializadas"
    },
    {
      icon: PeopleIcon,
      title: "Personas",
      description: "Equipos especializados y capacitados"
    },
    {
      icon: CubeIcon,
      title: "Productos",
      description: "Desarrollo de productos innovadores"
    }
  ];

  return (
    <section className={`clients-section ${isVisible ? 'visible' : ''}`}>
      <div className="clients-container">
        {/* Header */}
        <div className="clients-header">
          <h2 className="clients-title">Ellos Confían en Nosotros</h2>
          <div className="title-underline"></div>
        </div>

        {/* Company Logos */}
        <div className="companies-grid">
          {companies.map((company, index) => {
            return (
              <div
                key={company.name}
                className="company-logo"
                style={{ '--delay': `${index * 0.2}s` }}
              >
                <img
                  src={company.logo}
                  alt={`${company.name} logo`}
                  className="logo-image"
                />
              </div>
            );
          })}
        </div>

        {/* Star Icon */}
        <div className="star-divider">
          <StarIcon width={40} height={40} />
        </div>

        {/* Description */}
        <div className="clients-description">
          <p>
            En Greta Labs, tenemos tres enfoques: <span className="highlight">Personas, Servicios y Productos</span>. 
            Estos nos permiten desarrollar proyectos que realmente marcan la diferencia
          </p>
        </div>

        {/* Approaches */}
        <div className="approaches-container">
          <div className="wave-line">
            <svg viewBox="0 0 800 100" className="wave-svg">
              <path 
                d="M0,50 Q200,10 400,50 T800,50" 
                stroke="url(#waveGradient)" 
                strokeWidth="3" 
                fill="none"
              />
              <defs>
                <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#B30FDC"/>
                  <stop offset="50%" stopColor="#156CFF"/>
                  <stop offset="100%" stopColor="#B30FDC"/>
                </linearGradient>
              </defs>
            </svg>
          </div>

          <div className="approaches-grid">
            {approaches.map((approach, index) => {
              const IconComponent = approach.icon;
              return (
                <div 
                  key={approach.title}
                  className="approach-item"
                  style={{ '--delay': `${(index + 1) * 0.3}s` }}
                >
                  <div className="approach-icon">
                    <IconComponent width={80} height={80} />
                  </div>
                  {/* <h3 className="approach-title">{approach.title}</h3>
                  <p className="approach-description">{approach.description}</p> */}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;
