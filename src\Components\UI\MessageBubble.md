# MessageBubble Component

Componente reutilizable de burbuja de mensaje estilo chat para la aplicación Greta.

## Características

- ✅ Forma de burbuja de chat con cola/punta direccional
- ✅ Múltiples posiciones de cola (top-right, top-left, bottom-right, bottom-left)
- ✅ Variantes de estilo (primary, secondary)
- ✅ Animaciones de entrada suaves
- ✅ Efectos hover interactivos
- ✅ Diseño responsive
- ✅ Personalizable con props y estilos inline

## Uso Básico

```jsx
import MessageBubble from '../UI/MessageBubble';

// Mensaje básico
<MessageBubble 
  text="¡Hola! ¿En qué puedo ayudarte?"
  position="top-right"
  variant="primary"
/>
```

## Props

| Prop | Tipo | Default | Descripción |
|------|------|---------|-------------|
| `text` | string | - | Texto del mensaje (requerido) |
| `position` | string | 'top-right' | Posición de la cola: 'top-right', 'top-left', 'bottom-right', 'bottom-left' |
| `variant` | string | 'primary' | Variante de estilo: 'primary', 'secondary' |
| `className` | string | '' | Clases CSS adicionales |
| `style` | object | {} | Estilos inline adicionales |

## Ejemplos de Uso

### Mensaje de Notificación
```jsx
<MessageBubble 
  text="Tienes 5 nuevos mensajes"
  position="top-right"
  variant="primary"
  style={{ 
    position: 'absolute',
    top: '20px',
    right: '20px'
  }}
/>
```

### Mensaje de Pregunta
```jsx
<MessageBubble 
  text="¿Necesitas ayuda con algo?"
  position="bottom-left"
  variant="secondary"
  style={{ 
    position: 'absolute',
    bottom: '20px',
    left: '20px'
  }}
/>
```

### Mensaje con Rotación
```jsx
<MessageBubble 
  text="¡Proceso completado!"
  position="top-left"
  variant="primary"
  style={{ 
    transform: 'rotate(-5deg)',
    animationDelay: '0.5s'
  }}
/>
```

## Posiciones de Cola

- **top-right**: Cola apunta hacia abajo y derecha
- **top-left**: Cola apunta hacia abajo e izquierda  
- **bottom-right**: Cola apunta hacia arriba y derecha
- **bottom-left**: Cola apunta hacia arriba e izquierda

## Variantes

- **primary**: Texto azul (#156CFF) con fondo blanco
- **secondary**: Texto gris (#5B696E) con fondo gris claro

## Responsive

En dispositivos móviles (< 768px):
- La cola se oculta automáticamente
- El texto se ajusta para permitir múltiples líneas
- El tamaño se reduce para mejor adaptación

## Animaciones

- Animación de entrada con efecto de rebote
- Efectos hover con elevación y escala
- Delays personalizables con `animationDelay`

## Integración en PrismaSection

Ejemplo de uso actual en PrismaSection.jsx:

```jsx
<MessageBubble 
  text="Tienes 20 contratos por firmar"
  position="top-right"
  variant="primary"
  className="message-top"
  style={{
    position: 'absolute',
    top: '-80px',
    right: '-50px',
    transform: 'rotate(5deg)',
    animationDelay: '1.5s'
  }}
/>
```
