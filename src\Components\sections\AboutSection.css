.about-section {
  width: 100%;
  min-height: 100vh;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 100px 20px 80px;
}

.about-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 2;
}

/* Icono central animado */
.about-icon {
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(30px) scale(0.8);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.about-icon.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.icon-container {
  position: relative;
  display: inline-block;
}

.lightbulb-icon {
  width: 80px;
  height: 100px;
  position: relative;
  margin: 0 auto;
}

.bulb {
  width: 50px;
  height: 60px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: relative;
  margin: 0 auto;
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  animation: pulse 2s ease-in-out infinite;
}

.bulb::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.base {
  width: 30px;
  height: 20px;
  background: #4a5568;
  margin: 5px auto 0;
  border-radius: 0 0 10px 10px;
  position: relative;
}

.base::before {
  content: '';
  position: absolute;
  top: -3px;
  left: 0;
  width: 100%;
  height: 6px;
  background: #4a5568;
  border-radius: 3px;
}

/* Chispas animadas */
.spark {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #22c55e;
  border-radius: 50%;
  opacity: 0;
  animation: sparkle 2s ease-in-out infinite;
}

.spark-1 {
  top: 10px;
  left: -10px;
  animation-delay: 0s;
}

.spark-2 {
  top: 20px;
  right: -15px;
  animation-delay: 0.7s;
}

.spark-3 {
  top: 35px;
  left: -8px;
  animation-delay: 1.4s;
}

/* Texto principal */
.about-text {
  opacity: 0;
  transform: translateY(30px);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.about-text.visible {
  opacity: 1;
  transform: translateY(0);
}

.about-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 30px;
  line-height: 1.2;
}

.highlight-green {
  color: #22c55e;
  position: relative;
}

.highlight-green::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
  border-radius: 2px;
  opacity: 0;
  animation: underlineGrow 1s ease 1.5s forwards;
}

.about-description {
  font-size: 1.1rem;
  color: #e2e8f0;
  line-height: 1.8;
  max-width: 600px;
  margin: 0 auto;
}

.highlight-text {
  color: #a855f7;
  font-weight: 600;
}

/* Elementos decorativos */
.about-decorations {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.decoration-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #22c55e 50%, transparent 100%);
  opacity: 0.6;
}

.line-1 {
  width: 150px;
  top: 25%;
  left: 10%;
  transform: rotate(45deg);
  animation: float 4s ease-in-out infinite;
}

.line-2 {
  width: 100px;
  bottom: 30%;
  right: 15%;
  transform: rotate(-30deg);
  animation: float 4s ease-in-out infinite reverse;
}

.decoration-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  opacity: 0.7;
}

.dot-1 {
  top: 20%;
  right: 20%;
  animation: twinkle 3s ease-in-out infinite;
}

.dot-2 {
  bottom: 25%;
  left: 15%;
  animation: twinkle 3s ease-in-out infinite 1.5s;
}

/* Animaciones */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.8);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes underlineGrow {
  from {
    opacity: 0;
    width: 0;
  }
  to {
    opacity: 1;
    width: 100%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(45deg);
  }
  50% {
    transform: translateY(-15px) rotate(45deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-section {
    padding: 80px 15px 60px;
  }
  
  .about-title {
    font-size: 2rem;
  }
  
  .about-description {
    font-size: 1rem;
  }
  
  .lightbulb-icon {
    width: 60px;
    height: 80px;
  }
  
  .bulb {
    width: 40px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .about-title {
    font-size: 1.5rem;
  }
  
  .about-description {
    font-size: 0.9rem;
  }
  
  .decoration-line {
    width: 80px;
  }
}
