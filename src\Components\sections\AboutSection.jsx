import React, { useEffect, useState } from 'react';
import Header from '../layout/Header';
import './AboutSection.css';

/**
 * Sección About - Aparece dentro del rectángulo negro
 * Contiene la información sobre el laboratorio de ideas
 */
const AboutSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Activar animaciones después de un pequeño delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <section className="about-section">
      <Header variant="dark" />
      
      <div className="about-content">
        {/* Icono central */}
        <div className={`about-icon ${isVisible ? 'visible' : ''}`}>
          <div className="icon-container">
            <div className="lightbulb-icon">
              <div className="bulb"></div>
              <div className="base"></div>
              <div className="spark spark-1"></div>
              <div className="spark spark-2"></div>
              <div className="spark spark-3"></div>
            </div>
          </div>
        </div>

        {/* Texto principal */}
        <div className={`about-text ${isVisible ? 'visible' : ''}`}>
          <h2 className="about-title">
            Somos un Laboratorio de{' '}
            <span className="highlight-green">Ideas</span>
            <br />
            <span className="highlight-green">Innovadoras</span>
          </h2>
          
          <p className="about-description">
            Especializada en brindar soluciones de negocio en las líneas de{' '}
            <span className="highlight-text">outsourcing</span>,{' '}
            <span className="highlight-text">consultoría de seguridad</span> y{' '}
            <span className="highlight-text">proyectos</span>.
          </p>
        </div>

        {/* Elementos decorativos */}
        <div className="about-decorations">
          <div className="decoration-line line-1"></div>
          <div className="decoration-line line-2"></div>
          <div className="decoration-dot dot-1"></div>
          <div className="decoration-dot dot-2"></div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
