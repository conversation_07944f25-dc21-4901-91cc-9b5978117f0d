import React, { useEffect, useRef, useState } from 'react';
import { ANIMATION_CONFIG, getCurrentBreakpoint, getResponsiveConfig } from '../../config/animations';
import { throttle } from '../../Utils/animations/scrollUtils';
import './ZoomEffect.css';
import './ZoomEffectOverride.css';

/**
 * Componente para el efecto de zoom del rectángulo negro
 * que se expande desde pequeño hasta pantalla completa
 */
const ZoomEffect = ({ children }) => {
  const [scale, setScale] = useState(ANIMATION_CONFIG.zoomEffect.initialScale);
  const [opacity, setOpacity] = useState(ANIMATION_CONFIG.zoomEffect.initialOpacity);
  const [borderRadius, setBorderRadius] = useState(ANIMATION_CONFIG.zoomEffect.initialBorderRadius);
  const [translateY, setTranslateY] = useState(0);
  const containerRef = useRef(null);


  useEffect(() => {
    const handleScroll = throttle(() => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      // Obtener configuración responsive
      const breakpoint = getCurrentBreakpoint();
      const config = getResponsiveConfig(breakpoint, 'zoomEffect');

      // Calcular el progreso del scroll (0 a 1)
      const progress = Math.min(scrollY / (windowHeight * config.scrollDistance), 1);

      // Calcular la escala
      const newScale = config.initialScale + (progress * (config.finalScale - config.initialScale));

      // Calcular la opacidad
      const newOpacity = config.initialOpacity + (progress * (config.finalOpacity - config.initialOpacity));

      // Calcular el border radius (disminuye conforme se expande)
      const newBorderRadius = config.initialBorderRadius * (1 - progress);

      // Calcular el translateY para mover hacia el centro conforme se expande
      // Inicialmente está en 65vh, debe moverse hacia 0 (centro)
      const initialOffset = window.innerHeight * 0.65; // 65vh en pixels
      const newTranslateY = -initialOffset * progress;

      setScale(newScale);
      setOpacity(newOpacity);
      setBorderRadius(newBorderRadius);
      setTranslateY(newTranslateY);
    }, ANIMATION_CONFIG.scroll.throttleMs);

    window.addEventListener('scroll', handleScroll);

    // Llamar una vez para establecer valores iniciales
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className="zoom-effect-container"
      ref={containerRef}
      style={{
        transform: `translateY(${translateY}px)`,
        transition: `transform ${ANIMATION_CONFIG.zoomEffect.transitionDuration}s ${ANIMATION_CONFIG.zoomEffect.easingFunction}`
      }}
    >
      <div
        className="zoom-effect-content"
        style={{
          transform: `scale(${scale})`,
          opacity: opacity,
          borderRadius: `${borderRadius}px`,
          transition: `transform ${ANIMATION_CONFIG.zoomEffect.transitionDuration}s ${ANIMATION_CONFIG.zoomEffect.easingFunction},
                      opacity ${ANIMATION_CONFIG.zoomEffect.transitionDuration}s ${ANIMATION_CONFIG.zoomEffect.easingFunction},
                      border-radius ${ANIMATION_CONFIG.zoomEffect.transitionDuration}s ${ANIMATION_CONFIG.zoomEffect.easingFunction}`
        }}
      >
        {scale < 0.5 && (
          <div className="zoom-button-text">
            Empecemos!
          </div>
        )}
        {scale >= 0.5 && children}
      </div>
    </div>
  );
};

export default ZoomEffect;
