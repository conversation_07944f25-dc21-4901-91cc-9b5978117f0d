import { useState, useEffect } from 'react';
import FocoIcon from '../../assets/SVG/foco';
import './LabSection.css';

/**
 * Sección Lab - Primera sección del stack (fondo oscuro)
 * Muestra "Somos un Laboratorio de Ideas Innovadoras" con el ícono de foco
 */
const LabSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section className={`lab-section ${isVisible ? 'visible' : ''}`}>
      <div className="lab-container">
        {/* Contenido principal centrado */}
        <div className="lab-main-content">
          {/* Ícono de foco */}
          <div className="lab-icon">
            <FocoIcon width={100} height={120} />
          </div>

          <h1 className="lab-title">
            Somos un Laboratorio de <span className="highlight-green">Ideas</span>
            <br />
            <span className="highlight-green">Innovadoras</span>
          </h1>
          
          <p className="lab-subtitle">
            Especializada en brindar soluciones de negocio en las líneas de
            <br />
            <strong>outsourcing, consultoría de seguridad y proyectos.</strong>
          </p>
        </div>

        {/* Elementos decorativos */}
        <div className="lab-decorations">
          <div className="decoration-circle decoration-1"></div>
          <div className="decoration-circle decoration-2"></div>
          <div className="decoration-circle decoration-3"></div>
        </div>
      </div>
    </section>
  );
};

export default LabSection;
