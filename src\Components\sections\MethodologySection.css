.methodology-section {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%); */
  background: #F5FFF4;
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.methodology-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.methodology-container {
  max-width: 1400px;
  width: 100%;
}

/* Header */
.methodology-header {
  /* text-align: center; */
  margin-bottom: 80px;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.3s forwards;
}

.methodology-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.title-underline {
  width: 200px;
  height: 4px;
  background: linear-gradient(90deg, #00E5FF 0%, #ADFF4D 100%);
  margin: 0 0 40px 0;
  border-radius: 2px;
}

.methodology-description {
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.5rem;
  line-height: 1.6;
  color: #555;
}

/* Frameworks Grid */
.frameworks-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-top: 60px;
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
}

.framework-card-ms {
  /* border-radius: 30px; */
  /* padding: 40px 30px; */
  min-height: 500px;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  transform-origin: center;
}

/* .framework-card-ms:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
} */

/* Framework Header */
.framework-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  /* margin-bottom: 30px; */
  position: absolute;
  top: 3.5rem;
  left: 3rem;
}

/* Framework Header Variants */
.framework-header.dark {
  /* background: #2a2a2a; */
  color: white;
}

.framework-header.green {
  /* background: linear-gradient(135deg, #ADFF4D 0%, #00E5FF 100%); */
  color: #333;
}

.framework-header.light {
  /* background: #ffffff; */
  color: #333;
  /* border: 2px solid #e0e0e0; */
}

.framework-circle {
  position: absolute;
  right: 1.5rem;
  top: 1.9rem;
  transition: all 0.3s ease;
}

/* Cuando el mouse pasa por encima */
/* que se rote suavemente */
.framework-circle:hover svg {
  transition: all 0.3s ease;
  transform: rotate(45deg); 
}

/* cuando se hace click */
.framework-circle:active svg {
  transition: all 0.3s ease;
  transform: scale(0.9);
}

/* Cuando el mouse ya no está encima que regrese rotando 45 grados en reversa */
.framework-circle:not(:hover) svg {
  transition: all 0.3s ease;
  transform: rotate(0deg);
}

.framework-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  flex: 1;
}

.framework-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  transition: all 0.3s ease;
}

.framework-card-ms.dark .framework-arrow {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.framework-card-ms.green .framework-arrow {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.framework-card-ms.light .framework-arrow {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.framework-card-ms:hover .framework-arrow {
  transform: rotate(45deg);
}

/* Framework Content */
.framework-content {
  /* flex: 1; */
  position: absolute;
  top: 10rem;
  left: 4rem;
  width: 22vw;
  max-width: 350px;
}

/* Framework Card Variants */
.framework-content.dark {
  /* background: #2a2a2a; */
  color: white;
}

.framework-content.green {
  /* background: linear-gradient(135deg, #ADFF4D 0%, #00E5FF 100%); */
  color: #333;
}

.framework-content.light {
  /* background: #ffffff; */
  color: #333;
  /* border: 2px solid #e0e0e0; */
}

.framework-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.framework-feature {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 4px;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.framework-card-ms:hover .framework-feature {
  opacity: 1;
}

/* Decorative Elements */
/* .framework-card-ms::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
} */

/* .framework-card-ms:hover::before {
  opacity: 1;
} */

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .frameworks-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    max-width: 500px;
  }

  .framework-card-ms {
    min-height: 480px;
  }

  .framework-content {
    width: 50vw;
    max-width: 420px;
    left: 5.5rem;
    top: 12.7rem;
  }

  .framework-header {
    left: 5rem;
    top: 2.8rem;
  }

  .framework-circle {
    right: 3.5rem;
    top: 1.4rem;
  }
}

@media (max-width: 768px) {
  .methodology-section {
    padding: 60px 20px;
  }

  .methodology-title {
    font-size: 2.5rem;
  }

  .methodology-description {
    font-size: 1.1rem;
  }

  .framework-card-ms {
    min-height: 360px;
  }

  .framework-title {
    font-size: 1.6rem;
  }

  .framework-content {
    width: 60vw;
    max-width: 350px;
    left: 4.5rem;
    top: 10.8rem;
  }

  .framework-header {
    left: 4rem;
    top: 2.3rem;
  }

  .framework-circle {
    right: 3.7rem;
    top: .2rem;
  }

  .framework-feature {
    font-size: 1rem;
  }
}
