/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const CircleCustom2 = () => {

  return (
    <svg width="71" height="71" viewBox="0 0 71 71" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" y="0.5" width="70" height="70" rx="35" stroke="url(#paint0_linear_184_85)"/>
      <path d="M43.066 41.393L43.066 29.6079C43.0612 29.3885 43.0132 29.1723 42.9246 28.9715C42.7555 28.5667 42.4337 28.2449 42.0289 28.0758C41.8282 27.9873 41.6119 27.9392 41.3925 27.9344L29.6074 27.9344C29.3876 27.9344 29.17 27.9777 28.967 28.0618C28.7639 28.1459 28.5795 28.2692 28.4241 28.4246C28.1102 28.7384 27.9339 29.1641 27.9339 29.6079C27.9339 30.0517 28.1102 30.4774 28.4241 30.7912C28.7379 31.1051 29.1636 31.2814 29.6074 31.2814L37.3738 31.2696L28.4289 40.2145C28.1163 40.5271 27.9407 40.951 27.9407 41.393C27.9407 41.835 28.1163 42.259 28.4289 42.5715C28.7414 42.8841 29.1654 43.0597 29.6074 43.0597C30.0494 43.0597 30.4733 42.8841 30.7859 42.5715L39.7308 33.6266L39.719 41.393C39.7181 41.613 39.7608 41.831 39.8446 42.0345C39.9284 42.2379 40.0516 42.4228 40.2072 42.5784C40.3627 42.7339 40.5476 42.8572 40.751 42.9409C40.9545 43.0247 41.1725 43.0674 41.3925 43.0665C41.6125 43.0674 41.8305 43.0247 42.034 42.9409C42.2374 42.8572 42.4223 42.7339 42.5779 42.5784C42.7334 42.4228 42.8567 42.2379 42.9404 42.0345C43.0242 41.831 43.0669 41.613 43.066 41.393Z" fill="url(#paint1_linear_184_85)"/>
      <defs>
        <linearGradient id="paint0_linear_184_85" x1="35.5" y1="0" x2="35.5" y2="71" gradientUnits="userSpaceOnUse">
          <stop stop-color="#ADFF4D"/>
          <stop offset="1" stop-color="#17FFE4"/>
        </linearGradient>
        <linearGradient id="paint1_linear_184_85" x1="42.5662" y1="28.4363" x2="28.4299" y2="42.5725" gradientUnits="userSpaceOnUse">
          <stop stop-color="#ADFF4D"/>
          <stop offset="1" stop-color="#17FFE4"/>
        </linearGradient>
      </defs>
    </svg>
  );
};

export default CircleCustom2;
