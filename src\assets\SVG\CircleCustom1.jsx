/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const CircleCustom1 = () => {

  return (
    <svg width="73" height="71" viewBox="0 0 73 71" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" y="0.5" width="72" height="70" rx="35" stroke="url(#paint0_linear_184_82)"/>
      <path d="M44.066 41.393L44.066 29.6079C44.0612 29.3885 44.0132 29.1723 43.9246 28.9715C43.7555 28.5667 43.4337 28.2449 43.0289 28.0758C42.8282 27.9873 42.6119 27.9392 42.3925 27.9344L30.6074 27.9344C30.3876 27.9344 30.17 27.9777 29.967 28.0618C29.7639 28.1459 29.5795 28.2692 29.4241 28.4246C29.1102 28.7384 28.9339 29.1641 28.9339 29.6079C28.9339 30.0517 29.1102 30.4774 29.4241 30.7912C29.7379 31.1051 30.1636 31.2814 30.6074 31.2814L38.3738 31.2696L29.4289 40.2145C29.1163 40.5271 28.9407 40.951 28.9407 41.393C28.9407 41.835 29.1163 42.259 29.4289 42.5715C29.7414 42.8841 30.1654 43.0597 30.6074 43.0597C31.0494 43.0597 31.4733 42.8841 31.7859 42.5715L40.7308 33.6266L40.719 41.393C40.7181 41.613 40.7608 41.831 40.8446 42.0345C40.9284 42.2379 41.0516 42.4228 41.2072 42.5784C41.3627 42.7339 41.5476 42.8572 41.751 42.9409C41.9545 43.0247 42.1725 43.0674 42.3925 43.0665C42.6125 43.0674 42.8305 43.0247 43.034 42.9409C43.2374 42.8572 43.4223 42.7339 43.5779 42.5784C43.7334 42.4228 43.8567 42.2379 43.9404 42.0345C44.0242 41.831 44.0669 41.613 44.066 41.393Z" fill="url(#paint1_linear_184_82)"/>
      <defs>
        <linearGradient id="paint0_linear_184_82" x1="36.5" y1="0" x2="36.5" y2="71" gradientUnits="userSpaceOnUse">
          <stop stop-color="#B30FDC"/>
          <stop offset="1" stop-color="#156CFF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_184_82" x1="43.5662" y1="28.4363" x2="29.4299" y2="42.5725" gradientUnits="userSpaceOnUse">
          <stop stop-color="#B30FDC"/>
          <stop offset="1" stop-color="#156CFF"/>
        </linearGradient>
      </defs>
    </svg>
  );
};

export default CircleCustom1;
