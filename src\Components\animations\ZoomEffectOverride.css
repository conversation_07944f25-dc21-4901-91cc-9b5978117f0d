/* Override específico para posicionar el rectángulo negro debajo del texto del hero */
.zoom-effect-container {
  /* Posicionar el elemento inicialmente debajo del texto principal */
  align-items: flex-start !important;
  padding-top: 65vh !important; /* Aproximadamente debajo del texto del hero */
}

/* Ajustes responsive para el nuevo posicionamiento */
@media (max-width: 768px) {
  .zoom-effect-container {
    padding-top: 60vh !important;
  }
}

@media (max-width: 480px) {
  .zoom-effect-container {
    padding-top: 55vh !important;
  }
}
