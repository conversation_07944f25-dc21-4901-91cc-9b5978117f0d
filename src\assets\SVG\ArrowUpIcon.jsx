/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const ArrowUpIcon = () => {

  return (
    <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_80_442)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5927 1.53621L15.3069 6.25038L14.1285 7.42871L10.0035 3.30371L5.87853 7.42871L4.7002 6.25038L9.41436 1.53621C9.57063 1.37999 9.78256 1.29222 10.0035 1.29222C10.2245 1.29222 10.4364 1.37999 10.5927 1.53621Z" fill="#C1C0C0"/>
      </g>
      <defs>
        <clipPath id="clip0_80_442">
        <rect width="10" height="20" fill="white" transform="matrix(0 -1 1 0 0 10)"/>
        </clipPath>
      </defs>
    </svg>
  );
};

export default ArrowUpIcon;
