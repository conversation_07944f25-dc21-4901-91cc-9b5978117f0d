/**
 * Utilidades para manejo de animaciones basadas en scroll
 */

/**
 * Calcula el progreso del scroll entre dos puntos
 * @param {number} scrollY - Posición actual del scroll
 * @param {number} startPoint - Punto de inicio
 * @param {number} endPoint - Punto final
 * @returns {number} Progreso entre 0 y 1
 */
export const calculateScrollProgress = (scrollY, startPoint, endPoint) => {
  if (scrollY <= startPoint) return 0;
  if (scrollY >= endPoint) return 1;
  return (scrollY - startPoint) / (endPoint - startPoint);
};

/**
 * Interpola entre dos valores basado en el progreso
 * @param {number} start - Valor inicial
 * @param {number} end - Valor final
 * @param {number} progress - Progreso entre 0 y 1
 * @returns {number} Valor interpolado
 */
export const lerp = (start, end, progress) => {
  return start + (end - start) * progress;
};

/**
 * Aplica una función de easing cubic-bezier
 * @param {number} t - Tiempo normalizado (0-1)
 * @returns {number} Valor con easing aplicado
 */
export const easeOutCubic = (t) => {
  return 1 - Math.pow(1 - t, 3);
};

/**
 * Aplica una función de easing suave
 * @param {number} t - Tiempo normalizado (0-1)
 * @returns {number} Valor con easing aplicado
 */
export const easeInOutQuad = (t) => {
  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
};

/**
 * Throttle function para optimizar eventos de scroll
 * @param {Function} func - Función a ejecutar
 * @param {number} limit - Límite en milisegundos
 * @returns {Function} Función throttled
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Detecta si un elemento está visible en el viewport
 * @param {HTMLElement} element - Elemento a verificar
 * @param {number} threshold - Porcentaje de visibilidad requerido (0-1)
 * @returns {boolean} True si está visible
 */
export const isElementVisible = (element, threshold = 0.1) => {
  if (!element) return false;
  
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight;
  const elementHeight = rect.height;
  
  const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
  const visibilityRatio = visibleHeight / elementHeight;
  
  return visibilityRatio >= threshold;
};

/**
 * Hook personalizado para manejar scroll con throttling
 * @param {Function} callback - Función a ejecutar en scroll
 * @param {number} throttleMs - Milisegundos de throttle
 */
export const useThrottledScroll = (callback, throttleMs = 16) => {
  const throttledCallback = throttle(callback, throttleMs);
  
  React.useEffect(() => {
    window.addEventListener('scroll', throttledCallback);
    return () => window.removeEventListener('scroll', throttledCallback);
  }, [throttledCallback]);
};
