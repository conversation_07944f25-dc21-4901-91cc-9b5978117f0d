# Guía de Assets para Greta Landing Page

## 📁 Estructura de Assets Creada

He creado la estructura completa de assets para tu landing page. Aquí tienes la lista de archivos que necesitas completar con el contenido real de Figma:

### 🎨 Iconos SVG (src/assets/SVG/)

#### ✅ Completado
- `foco.jsx` - Ícono de bombilla/foco (ya convertido a componente React)

#### 🔄 Pendientes de completar
Estos archivos están creados como placeholders. Necesitas reemplazar el contenido SVG con el código real de Figma:

1. **StarIcon.jsx** - Ícono de estrella para la sección de clientes
2. **GearsIcon.jsx** - Ícono de engranajes para servicios y metodología  
3. **PeopleIcon.jsx** - Ícono de personas para los enfoques
4. **CubeIcon.jsx** - Ícono de cubo para productos
5. **HomeIcon.jsx** - Ícono de casa para Outsourcing
6. **ShieldIcon.jsx** - Ícono de escudo para Consultoría de seguridad
7. **ArrowIcon.jsx** - Ícono de flecha para botones

### 🏢 Logos de Empresas (src/assets/logos/)

Estos archivos están creados como placeholders de texto. Necesitas reemplazar con los logos reales:

1. **PetroTalLogo.jsx** - Logo de PetroTal
2. **RansaLogo.jsx** - Logo de Ransa  
3. **NexaLogo.jsx** - Logo de Nexa
4. **AlfaparfLogo.jsx** - Logo de Alfaparf Professional
5. **PrismaLogo.jsx** - Logo de Prisma

### 🖼️ Imágenes (src/assets/images/)

1. **ChatBotAvatar.jsx** - Avatar del ChatBot Greta para la sección Prisma

## 🔧 Cómo Reemplazar los Assets

### Para Iconos SVG:

1. Ve al archivo correspondiente (ej: `src/assets/SVG/StarIcon.jsx`)
2. Copia el código SVG de Figma
3. Reemplaza el contenido dentro del `<svg>` tag, manteniendo la estructura del componente React
4. Asegúrate de mantener las props `width`, `height`, `className` para que sean configurables

Ejemplo de estructura a mantener:
```jsx
const StarIcon = ({ width = 24, height = 24, className = "" }) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* AQUÍ VA TU CÓDIGO SVG DE FIGMA */}
    </svg>
  );
};
```

### Para Logos:

1. Ve al archivo correspondiente (ej: `src/assets/logos/PetroTalLogo.jsx`)
2. Reemplaza el contenido del `<svg>` con el logo real
3. Ajusta las dimensiones si es necesario

### Para Imágenes:

1. Si tienes la imagen como archivo (PNG, JPG), colócala en `src/assets/images/`
2. Si es SVG, puedes usar la misma estructura de componente

## 📋 Secciones Implementadas

### ✅ HeroSection - Página inicial (Fondo claro)
- Logo Greta en esquina superior izquierda
- Botón "Empecemos!" en esquina superior derecha
- Texto "¿Tienes un proyecto por hacer?"
- Subtítulo "Investigamos para Desarrollar Soluciones"
- Botón negro "Empecemos!" centrado

### ✅ Sección 1 - LabSection (Fondo oscuro)
- Ícono de foco centrado
- Texto "Somos un Laboratorio de Ideas Innovadoras"
- Subtítulo sobre especialización en outsourcing, consultoría y proyectos

### ✅ Sección 2 - ClientsSection (Fondo claro)
- "Ellos Confían en Nosotros"
- Logos de empresas: PetroTal, Ransa, Nexa, Alfaparf
- Ícono de estrella
- Texto sobre los 3 enfoques
- Iconos de Servicios, Personas, Productos con línea ondulada

### ✅ Sección 3 - Servicios (Fondo claro)
- "Nuestros Servicios"
- Tarjeta de "Gestión de Proyectos"
- Tarjetas de "Outsourcing" (fondo oscuro) y "Consultoría de seguridad" (fondo verde)

### ✅ Sección 4 - Metodología (Fondo claro)
- "Nuestra Metodología"
- 3 tarjetas: IT Blackbook, Security Blackbook, Greta Playbook

### ✅ Sección 5 - Prisma (Fondo azul)
- Logo de Prisma
- "El software para abogados hecho por abogados"
- Features con iconos
- Módulos: ChatBot Greta, Prisma Contratos, Prisma Procesos
- Avatar del ChatBot

### ✅ Sección 6 - CTA Final (Fondo oscuro)
- Logos de empresas en la parte superior
- "¿Tienes un proyecto por hacer?"
- Botón "Empecemos"

## 🚀 Estructura de Rutas

### **Página Principal (`/`)**
- Solo muestra el HeroSection con fondo claro
- Botones "Empecemos!" redirigen a `/empecemos`
- Sin scroll, página estática

### **Página de Secciones (`/empecemos`)**
- Todas las 6 secciones apiladas con scroll normal
- Efecto de apilamiento visual (cada sección aparece encima de la anterior)
- Scroll fluido entre secciones

## 🚀 Próximos Pasos

1. **Reemplaza los assets** con el contenido real de Figma
2. **Prueba la aplicación** ejecutando `npm run dev`
3. **Navega entre rutas** usando los botones "Empecemos!"
4. **Ajusta colores y espaciados** si es necesario
5. **Optimiza las animaciones** según tus preferencias

## 💡 Notas Importantes

- **Rutas implementadas**: `/` (página principal) y `/empecemos` (secciones)
- Todos los componentes están configurados para ser responsivos
- Las animaciones se activan automáticamente al cargar cada sección
- Los gradientes y colores están basados en tu paleta de colores
- Los iconos son componentes reutilizables con props configurables
- React Router DOM instalado y configurado
