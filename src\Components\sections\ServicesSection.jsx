import { useState, useEffect } from 'react';
import HomeIcon from '../../assets/SVG/HomeIcon';
import ShieldIcon from '../../assets/SVG/ShieldIcon';
import GearsIcon from '../../assets/SVG/GearsIcon';
import ArrowRightIcon from '../../assets/SVG/ArrowRightIcon';
import './ServicesSection.css';

/**
 * Sección de Servicios - Tercera sección
 * Muestra los servicios principales: Outsourcing y Consultoría de seguridad
 */
const ServicesSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const mainServices = [
    {
      id: 1,
      title: "Outsourcing",
      description: "Soluciones tecnológicas especializadas para optimizar tus procesos",
      icon: HomeIcon,
      color: "dark",
      buttonText: "Comunícate con nosotros"
    },
    {
      id: 2,
      title: "Consultoría de seguridad",
      description: "Protección y seguridad integral para tu infraestructura digital",
      icon: ShieldIcon,
      color: "green",
      buttonText: "Comunícate con nosotros"
    }
  ];

  return (
    <section className={`services-section ${isVisible ? 'visible' : ''}`}>
      <div className="services-container">
        {/* Top Section - Header and Management Card */}
        <div className="services-top-section">
          {/* Header */}
          <div className="services-header">
            <h2 className="services-title">Nuestros Servicios</h2>
            <div className="title-underline"></div>
            <p className="services-subtitle">
              En Greta Labs desarrollamos soluciones que optimizan los procesos y refuerzan la infraestructura digital de las empresas.
            </p>
          </div>

          {/* Management Card */}
          <div className="management-card">
            <div className="management-content">
              <h3 className="management-title">Gestión de Proyectos</h3>
              <button className="management-button">
                Comunícate con nosotros
                <ArrowRightIcon />
              </button>
            </div>
            <div className="management-icon">
              <GearsIcon width={60} height={60} />
            </div>
          </div>
        </div>

        {/* Main Services Grid */}
        <div className="services-grid">
          {mainServices.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div
                key={service.id}
                className={`service-card ${service.color}`}
                style={{ '--delay': `${index * 0.3}s` }}
              >
                <div className="service-content">
                  <h3 className="service-title">{service.title}</h3>
                  <p className="service-description">{service.description}</p>
                  <button className={`service-button ${service.id === 1 ? 'outsourcing-button' : ''}`}>
                    {service.buttonText}
                    <ArrowRightIcon color='#000000' />
                  </button>
                </div>
                <div className="service-icon">
                  <IconComponent width={80} height={80} />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
