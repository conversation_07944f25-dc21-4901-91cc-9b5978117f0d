import React, { useEffect, useRef, useState } from 'react';
import './CardStackEffect.css';

/**
 * Componente para el efecto de tarjetas que aparecen desde abajo
 * como si se estuvieran apilando una encima de otra
 */
const CardStackEffect = ({ sections = [] }) => {
  const [visibleSections, setVisibleSections] = useState(new Set([0]));
  const sectionRefs = useRef([]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      sections.forEach((_, index) => {
        const triggerPoint = windowHeight * (index + 1);
        
        if (scrollY >= triggerPoint - windowHeight * 0.5) {
          setVisibleSections(prev => new Set([...prev, index]));
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [sections]);

  return (
    <div className="card-stack-container">
      {sections.map((section, index) => (
        <div
          key={index}
          ref={el => sectionRefs.current[index] = el}
          className={`card-section ${visibleSections.has(index) ? 'visible' : ''}`}
          style={{
            zIndex: sections.length - index,
            transform: `translateY(${visibleSections.has(index) ? '0' : '100vh'})`,
          }}
        >
          {section.component}
        </div>
      ))}
    </div>
  );
};

export default CardStackEffect;
