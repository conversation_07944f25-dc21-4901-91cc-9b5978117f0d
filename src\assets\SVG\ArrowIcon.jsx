/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const ArrowIcon = () => {
  return (
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M25.5718 22.7915L25.5718 3.20839C25.5639 2.84386 25.484 2.48449 25.3368 2.1509C25.0559 1.47826 24.5212 0.943491 23.8485 0.662584C23.5149 0.515403 23.1556 0.435543 22.791 0.427587L3.2079 0.427589C2.84272 0.427589 2.48112 0.499516 2.14373 0.639264C1.80635 0.779013 1.4998 0.983846 1.24157 1.24207C0.720073 1.76357 0.427096 2.47088 0.427096 3.20839C0.427096 3.94591 0.720074 4.65322 1.24158 5.17472C1.76308 5.69622 2.47039 5.9892 3.2079 5.9892L16.1132 5.96961L1.24959 20.8332C0.73021 21.3526 0.438426 22.057 0.438426 22.7915C0.438426 23.526 0.730211 24.2305 1.24959 24.7498C1.76896 25.2692 2.47339 25.561 3.2079 25.561C3.94241 25.561 4.64684 25.2692 5.16621 24.7498L20.0298 9.88624L20.0102 22.7915C20.0087 23.1571 20.0796 23.5194 20.2188 23.8575C20.3581 24.1955 20.5628 24.5027 20.8214 24.7612C21.0799 25.0197 21.387 25.2245 21.7251 25.3637C22.0631 25.5029 22.4254 25.5738 22.791 25.5723C23.1566 25.5738 23.5189 25.5029 23.857 25.3637C24.195 25.2245 24.5022 25.0197 24.7607 24.7612C25.0192 24.5027 25.224 24.1955 25.3632 23.8575C25.5024 23.5194 25.5733 23.1571 25.5718 22.7915Z" fill="white"/>
    </svg>
  );
};

export default ArrowIcon;
