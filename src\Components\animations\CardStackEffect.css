.card-stack-container {
  position: relative;
  width: 100%;
}

.card-section {
  position: sticky;
  top: 0;
  width: 100%;
  min-height: 100vh;
  background: white;
  border-radius: 20px 20px 0 0;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-section:first-child {
  border-radius: 0;
  box-shadow: none;
}

.card-section.visible {
  transform: translateY(0) !important;
}

/* Efecto de apilamiento */
.card-section:not(:first-child) {
  margin-top: -20px;
}

/* Animación de entrada */
.card-section:not(.visible) {
  transform: translateY(100vh);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-section {
    border-radius: 15px 15px 0 0;
    margin-top: -15px;
  }
  
  .card-section:first-child {
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .card-section {
    border-radius: 10px 10px 0 0;
    margin-top: -10px;
  }
  
  .card-section:first-child {
    border-radius: 0;
  }
}
