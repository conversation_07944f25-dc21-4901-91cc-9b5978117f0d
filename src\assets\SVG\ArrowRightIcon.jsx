/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const ArrowRightIcon = ({color = "#ffffff"}) => {

  return (
    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_78_306)">
        <path d="M4 8.5H20M20 8.5L14 14.5M20 8.5L14 2.5" stroke="#272727" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      <defs>
        <clipPath id="clip0_78_306">
        <rect width="20" height="20" fill={color} transform="matrix(0 -1 1 0 0 20.5)"/>
        </clipPath>
      </defs>
    </svg>
  );
};

export default ArrowRightIcon;
