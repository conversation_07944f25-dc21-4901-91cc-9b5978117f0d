.prisma-section {
  min-height: 100vh;
  background: #F9FDFF;
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
  position: relative;
  overflow: hidden;
}

.prisma-section.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Gradientes radiales de fondo */
.prisma-section::before {
  content: '';
  position: absolute;
  top: -30%;
  left: 0%;
  width: 1100px;
  height: 1100px;
  background: radial-gradient(50% 50% at 50% 50%, rgba(96, 212, 250, 0.25) 0%, rgba(96, 212, 250, 0) 100%);
  backdrop-filter: blur(140.5px);
  border-radius: 50%;
  z-index: 0;
}

.prisma-section::after {
  content: '';
  position: absolute;
  top: -30%;
  right: 0%;
  width: 1100px;
  height: 1100px;
  background: radial-gradient(50% 50% at 50% 50%, rgba(21, 108, 255, 0.25) 0%, rgba(21, 108, 255, 0) 100%);
  backdrop-filter: blur(140.5px);
  border-radius: 50%;
  z-index: 0;
}

.prisma-container {
  max-width: 1400px;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Header */
.prisma-header {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.3s forwards;
}

.prisma-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 30px;
  /* padding-top: 40px; */
}

.prisma-logo {
  margin-top: 30px;
  opacity: 0;
  animation: fadeIn 1s ease 0.5s forwards;
}

.prisma-subtitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #000000;
  margin-bottom: 40px;
  line-height: 1.2;
}

.prisma-subtitle .highlight {
  background: linear-gradient(90deg, #156CFF 0%, #156CFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Features */
.prisma-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  margin-bottom: 40px;
  justify-items: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.prisma-feature {
  display: flex;
  align-items: center;
  gap: 10px;
  /* background: rgba(255, 255, 255, 0.9); */
  padding: 10px 15px;
  /* border-radius: 50px; */
  backdrop-filter: blur(10px);
  /* border: 1px solid rgba(255, 255, 255, 0.3); */
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.6s ease var(--delay) forwards;
  transition: all 0.3s ease;
}

/* Third feature spans both columns and centers */
.prisma-feature:nth-child(3) {
  grid-column: 1 / -1;
  justify-self: center;
}

/* .prisma-feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
} */

.feature-icon {
  font-size: 1.5rem;
}

.feature-text {
  font-size: 1rem;
  font-weight: 600;
  color: #000000;
}

.feature-text.highlight1 {
  color: #156CFF;
}

/* Prisma Button */
.prisma-button {
  background: transparent;
  color: #156CFF;
  border: 2px solid #156CFF;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  animation: slideInUp 0.8s ease 1s forwards;
}

.prisma-button:hover {
  transform: translateY(-3px);
  background: #156CFF;
  color: white;
}

/* Main Content */
.prisma-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

/* Modules */
.prisma-modules {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.prisma-module {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInLeft 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.prisma-module:hover {
  transform: translateX(5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.prisma-module.expanded {
  background: rgba(255, 255, 255, 0.98);
  /* border: 2px solid #156CFF; */
  box-shadow: 0 8px 25px rgba(21, 108, 255, 0.15);
}

.prisma-module.collapsed {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(91, 105, 110, 0.3);
}

.module-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 0;
  justify-content: space-between;
  width: 100%;
}

.prisma-module.expanded .module-header {
  margin-bottom: 15px;
}

.module-icon {
  font-size: 1.8rem;
}

.module-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #000000;
  margin: 0;
  flex: 1;
}

.module-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* Colores para los iconos según el estado */
.prisma-module.expanded .module-icon,
.prisma-module.expanded .module-title,
.prisma-module.expanded .module-arrow {
  color: #156CFF;
}

.prisma-module.collapsed .module-icon,
.prisma-module.collapsed .module-title,
.prisma-module.collapsed .module-arrow {
  color: #5B696E;
}

.module-description {
  font-size: 1rem;
  color: #555;
  line-height: 1.5;
  margin: 0;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.module-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 15px;
  display: inline-block;
}

/* ChatBot Container */
.chatbot-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  opacity: 0;
  animation: slideInRight 0.8s ease 1.2s forwards;
  position: relative;
}

.chatbot-messages {
  position: relative;
  width: 100%;
  height: 0;
}

/* Estilos específicos para los mensajes en esta sección */
.chatbot-container .message-top {
  z-index: 3;
}

.chatbot-container .message-bottom {
  z-index: 3;
}

.chatbot-avatar {
  margin: 0;
  transition: transform 0.3s ease;
  z-index: 1;
  position: relative;
}

.chatbot-avatar:hover {
  transform: scale(1.05);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .prisma-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .chatbot-container .message-top,
  .chatbot-container .message-bottom {
    position: relative !important;
    margin: 10px 0;
    transform: none !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
  }

  .chatbot-messages {
    position: static;
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .prisma-section {
    padding: 60px 20px;
  }

  .prisma-subtitle {
    font-size: 2rem;
  }

  .prisma-features {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 300px;
  }

  .prisma-feature {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .prisma-feature:nth-child(3) {
    grid-column: 1;
  }

  .prisma-section::before,
  .prisma-section::after {
    width: 400px;
    height: 400px;
  }
}
