/* Estilos base del contenedor - posicionado fijo sobre el hero */
.zoom-effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 100;
  transition: all 0.3s ease;
}



.zoom-effect-content {
  /* Tamaño base que se escalará */
  width: 200 px;
  height: 50 px;
  background: #000000;
  border-radius: 25px;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out, border-radius 0.1s ease-out;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  pointer-events: auto;
  /* Estilo inicial como botón */
  color: white;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  /* Centrar el elemento */
  margin: 0 auto;
}

/* Cuando está completamente expandido, cambiar a pantalla completa */
.zoom-effect-content[style*="scale(1)"] {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

/* Texto del botón cuando está pequeño - mismas dimensiones que el botón del header */
.zoom-button-text {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  pointer-events: none;
  white-space: nowrap;
  padding: 12px 24px;
}

/* Cuando está completamente expandido, quitar el border-radius */
.zoom-effect-content[style*="scale(1)"] {
  border-radius: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .zoom-effect-container.static .zoom-effect-content {
    width: 180px;
    height: 45px;
  }
  
  .zoom-effect-content {
    border-radius: 15px;
  }
  
  .zoom-button-text {
    font-size: 0.9rem;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .zoom-effect-container.static .zoom-effect-content {
    width: 160px;
    height: 40px;
  }
  
  .zoom-effect-content {
    border-radius: 10px;
  }
  
  .zoom-button-text {
    font-size: 0.9rem;
    padding: 10px 20px;
  }
}
