.hero-section {
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  overflow: hidden;
}

.hero-content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}



/* Contenido principal centrado */
.hero-main-content {
  text-align: center;
  max-width: 800px;
  padding: 0 40px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 30px;
  line-height: 1.2;
}

.highlight {
  background: linear-gradient(90deg, #C636FF 0%, #156CFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-subtitle {
  font-size: 1.4rem;
  color: #6b7280;
  margin-bottom: 50px;
  font-weight: 400;
}

/* Botón negro centrado */
.cta-button-dark {
  background: #1f2937;
  color: white;
  border: none;
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(31, 41, 55, 0.3);
}

.cta-button-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.4);
  background: #374151;
}

/* Líneas decorativas púrpuras */
.decorative-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.purple-line {
  position: absolute;
  height: 3px;
  background: linear-gradient(90deg, transparent 0%, #a855f7 50%, transparent 100%);
  opacity: 0.8;
}

.line-1 {
  width: 400px;
  top: 30%;
  left: 5%;
  transform: rotate(15deg);
  animation: float 4s ease-in-out infinite;
}

.line-2 {
  width: 300px;
  bottom: 25%;
  right: 8%;
  transform: rotate(-20deg);
  animation: float 4s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(15deg);
  }
  50% {
    transform: translateY(-20px) rotate(15deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-main-content {
    padding: 0 20px;
  }

  .purple-line {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .greta-text {
    font-size: 1.2rem;
  }

  .cta-button-dark {
    padding: 12px 30px;
    font-size: 1rem;
  }
}
