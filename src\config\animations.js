/**
 * Configuración centralizada para todas las animaciones del proyecto
 * Permite ajustar fácilmente los parámetros de las animaciones
 */

export const ANIMATION_CONFIG = {
  // Configuración del efecto de zoom
  zoomEffect: {
    // Escala inicial del rectángulo negro (0.15 = 15% del tamaño para ser visible como botón)
    initialScale: 0.2,

    // Escala final (1 = 100% del tamaño de pantalla)
    finalScale: 1.0,

    // Opacidad inicial (sin semitransparencia)
    initialOpacity: 1.0,

    // Opacidad final
    finalOpacity: 1.0,

    // Altura de scroll necesaria para completar la animación (en viewport heights)
    scrollDistance: 2, // 2 = dos veces la altura de la pantalla

    // Duración de la transición CSS (en segundos)
    transitionDuration: 0.1,

    // Función de easing CSS
    easingFunction: 'ease-out',

    // Border radius inicial (en px) - más redondeado para parecer un botón
    initialBorderRadius: 25,

    // Border radius final
    finalBorderRadius: 0
  },

  // Configuración del efecto de tarjetas apiladas
  cardStackEffect: {
    // Offset entre tarjetas (en px negativos)
    cardOffset: -20,

    // Duración de la animación de entrada (en segundos)
    animationDuration: 0.8,

    // Función de easing para la animación
    easingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',

    // Porcentaje de la pantalla que debe scrollearse para activar la siguiente tarjeta
    triggerThreshold: 0.5, // 0.5 = 50% de la altura de pantalla

    // Sombra de las tarjetas
    cardShadow: '0 -10px 30px rgba(0, 0, 0, 0.1)',

    // Border radius de las tarjetas (excepto la primera)
    cardBorderRadius: 20
  },

  // Configuración de animaciones de secciones individuales
  sections: {
    hero: {
      // Animación del logo
      logoAnimation: {
        duration: 1,
        delay: 0,
        easing: 'ease-out'
      },

      // Animación del título
      titleAnimation: {
        duration: 1,
        delay: 0.2,
        easing: 'ease-out'
      },

      // Animación del botón CTA
      ctaAnimation: {
        duration: 1,
        delay: 0.4,
        easing: 'ease-out'
      },

      // Animación de elementos decorativos
      decorativeAnimation: {
        duration: 3,
        easing: 'ease-in-out'
      }
    },

    services: {
      // Animación del header de servicios
      headerAnimation: {
        duration: 1,
        delay: 0,
        easing: 'ease-out'
      },

      // Animación de las tarjetas de servicios
      cardAnimation: {
        duration: 0.8,
        staggerDelay: 0.2, // Delay entre cada tarjeta
        easing: 'ease-out'
      },

      // Animación hover de las tarjetas
      cardHover: {
        translateY: -10,
        duration: 0.3,
        easing: 'ease'
      }
    }
  },

  // Configuración de scroll
  scroll: {
    // Throttle para eventos de scroll (en ms)
    throttleMs: 16, // ~60fps

    // Smooth scroll behavior
    smoothScroll: true,

    // Offset para detección de visibilidad de elementos
    visibilityThreshold: 0.1 // 10% del elemento debe ser visible
  },

  // Configuración responsive
  responsive: {
    mobile: {
      // Ajustes para móviles (max-width: 768px)
      zoomEffect: {
        initialBorderRadius: 10,
        scrollDistance: 1.5
      },
      cardStackEffect: {
        cardOffset: -10,
        cardBorderRadius: 10
      }
    },

    tablet: {
      // Ajustes para tablets (max-width: 1024px)
      zoomEffect: {
        initialBorderRadius: 15,
        scrollDistance: 1.8
      },
      cardStackEffect: {
        cardOffset: -15,
        cardBorderRadius: 15
      }
    }
  },

  // Configuración de performance
  performance: {
    // Usar transform3d para activar aceleración GPU
    useGPUAcceleration: true,

    // Reducir animaciones en dispositivos con batería baja
    respectsPowerSaveMode: true,

    // Pausar animaciones cuando la pestaña no está visible
    pauseOnTabHidden: true
  }
};

/**
 * Función helper para obtener configuración responsive
 * @param {string} breakpoint - 'mobile', 'tablet', 'desktop'
 * @param {string} effect - 'zoomEffect', 'cardStackEffect', etc.
 * @returns {object} Configuración para el breakpoint específico
 */
export const getResponsiveConfig = (breakpoint, effect) => {
  const baseConfig = ANIMATION_CONFIG[effect];
  const responsiveConfig = ANIMATION_CONFIG.responsive[breakpoint]?.[effect] || {};

  return { ...baseConfig, ...responsiveConfig };
};

/**
 * Función helper para detectar el breakpoint actual
 * @returns {string} 'mobile', 'tablet', o 'desktop'
 */
export const getCurrentBreakpoint = () => {
  const width = window.innerWidth;

  if (width <= 768) return 'mobile';
  if (width <= 1024) return 'tablet';
  return 'desktop';
};
