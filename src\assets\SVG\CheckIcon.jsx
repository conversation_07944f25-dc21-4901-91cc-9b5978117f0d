/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const CheckIcon = () => {

  return (
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.2744 3.06122C12.4931 2.82099 12.7595 2.62907 13.0566 2.49774C13.3538 2.36642 13.675 2.29858 13.9999 2.29858C14.3247 2.29858 14.646 2.36642 14.9431 2.49774C15.2403 2.62907 15.5067 2.82099 15.7254 3.06122L16.9154 4.36789C17.1464 4.62154 17.4306 4.82111 17.7476 4.95239C18.0646 5.08367 18.4066 5.14342 18.7494 5.12739L20.5146 5.04572C20.8389 5.03044 21.1629 5.08303 21.4658 5.20013C21.7687 5.31722 22.0438 5.49625 22.2735 5.72576C22.5032 5.95527 22.6825 6.2302 22.7999 6.53297C22.9173 6.83573 22.9702 7.15967 22.9552 7.48405L22.8724 9.25039C22.8563 9.59312 22.9161 9.93517 23.0474 10.2522C23.1787 10.5692 23.3782 10.8533 23.6319 11.0844L24.9385 12.2744C25.1788 12.4931 25.3707 12.7595 25.502 13.0566C25.6334 13.3538 25.7012 13.675 25.7012 13.9999C25.7012 14.3247 25.6334 14.646 25.502 14.9431C25.3707 15.2403 25.1788 15.5067 24.9385 15.7254L23.6319 16.9154C23.3782 17.1464 23.1787 17.4306 23.0474 17.7476C22.9161 18.0646 22.8563 18.4066 22.8724 18.7494L22.954 20.5146C22.9693 20.8389 22.9167 21.1629 22.7996 21.4658C22.6825 21.7687 22.5035 22.0438 22.274 22.2735C22.0445 22.5032 21.7696 22.6825 21.4668 22.7999C21.164 22.9173 20.8401 22.9702 20.5157 22.9552L18.7494 22.8724C18.4066 22.8563 18.0646 22.9161 17.7476 23.0474C17.4306 23.1787 17.1464 23.3782 16.9154 23.6319L15.7254 24.9385C15.5067 25.1788 15.2403 25.3707 14.9431 25.502C14.646 25.6334 14.3247 25.7012 13.9999 25.7012C13.675 25.7012 13.3538 25.6334 13.0566 25.502C12.7595 25.3707 12.4931 25.1788 12.2744 24.9385L11.0844 23.6319C10.8533 23.3782 10.5692 23.1787 10.2522 23.0474C9.93517 22.9161 9.59312 22.8563 9.25039 22.8724L7.48522 22.954C7.16085 22.9693 6.83686 22.9167 6.53399 22.7996C6.23111 22.6825 5.956 22.5035 5.72628 22.274C5.49655 22.0445 5.31726 21.7696 5.19987 21.4668C5.08248 21.164 5.02958 20.8401 5.04455 20.5157L5.12739 18.7494C5.14342 18.4066 5.08367 18.0646 4.95239 17.7476C4.82111 17.4306 4.62154 17.1464 4.36789 16.9154L3.06122 15.7254C2.82099 15.5067 2.62907 15.2403 2.49774 14.9431C2.36642 14.646 2.29858 14.3247 2.29858 13.9999C2.29858 13.675 2.36642 13.3538 2.49774 13.0566C2.62907 12.7595 2.82099 12.4931 3.06122 12.2744L4.36789 11.0844C4.62154 10.8533 4.82111 10.5692 4.95239 10.2522C5.08367 9.93517 5.14342 9.59312 5.12739 9.25039L5.04572 7.48522C5.03044 7.16085 5.08303 6.83686 5.20013 6.53399C5.31722 6.23111 5.49625 5.956 5.72576 5.72628C5.95527 5.49655 6.2302 5.31726 6.53297 5.19987C6.83573 5.08248 7.15967 5.02958 7.48405 5.04455L9.25039 5.12739C9.59312 5.14342 9.93517 5.08367 10.2522 4.95239C10.5692 4.82111 10.8533 4.62154 11.0844 4.36789L12.2744 3.06122Z" stroke="#B30FDC" stroke-width="1.75"/>
      <path d="M10.5 13.9998L12.8333 16.3332L17.5 11.6665" stroke="#B30FDC" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  );
};

export default CheckIcon;
