/**
 * Componente SVG del ícono de flecha
 * Usado en botones y enlaces
 */
const ArrowDownIcon = () => {

  return (
    <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_80_451)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.4073 8.46379L4.69314 3.74962L5.87147 2.57129L9.99647 6.69629L14.1215 2.57129L15.2998 3.74962L10.5856 8.46379C10.4294 8.62001 10.2174 8.70778 9.99647 8.70778C9.7755 8.70778 9.56358 8.62001 9.4073 8.46379Z" fill="#C1C0C0"/>
      </g>
      <defs>
        <clipPath id="clip0_80_451">
        <rect width="10" height="20" fill="white" transform="matrix(0 1 -1 0 20 0)"/>
        </clipPath>
      </defs>
    </svg>
  );
};

export default ArrowDownIcon;
