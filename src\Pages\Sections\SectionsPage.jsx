import Header from '../../Components/layout/Header';
import LabSection from '../../Components/sections/LabSection';
import ClientsSection from '../../Components/sections/ClientsSection';
import ServicesSection from '../../Components/sections/ServicesSection';
import MethodologySection from '../../Components/sections/MethodologySection';
import PrismaSection from '../../Components/sections/PrismaSection';
import CTASection from '../../Components/sections/CTASection';
import useMouseWheelScroll from '../../hooks/useMouseWheelScroll';
import './SectionsPage.css';

/**
 * Página de secciones con efecto mouse wheel
 * Cada sección se mantiene fija hasta que la siguiente la cubre completamente
 * Solo permite avanzar cuando la transición actual está completa
 * Incluye header dinámico que cambia según el fondo de la sección
 */
const SectionsPage = () => {
  // Array de componentes de secciones
  const sections = [
    LabSection,
    ClientsSection,
    ServicesSection,
    MethodologySection,
    PrismaSection,
    CTASection
  ];

  // Hook para manejar el scroll tipo mouse wheel
  const {
    currentSection,
    isTransitioning,
    getSectionState,
    goToSection
  } = useMouseWheelScroll(sections.length);

  // Determinar qué logo usar según la sección actual
  // Solo LabSection (índice 0) usa logo blanco, el resto usa logo verde
  const useWhiteLogo = currentSection === 0; // LabSection

  return (
    <div className={`sections-page ${isTransitioning ? 'transitioning' : ''}`}>
      {/* Header dinámico fijo */}
      <Header useWhiteLogo={useWhiteLogo} />

      {/* Contenedor de secciones con efecto mouse wheel */}
      <div className="sections-container">
        {sections.map((SectionComponent, index) => (
          <div
            key={index}
            className={`section-wrapper ${getSectionState(index)}`}
            data-section={index}
          >
            <SectionComponent />
          </div>
        ))}
      </div>

      {/* Indicador de sección actual (opcional) */}
      <div className="section-indicator">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`indicator-dot ${index === currentSection ? 'active' : ''}`}
            onClick={() => goToSection(index)}
            title={`Ir a sección ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default SectionsPage;
