/* ===== MOUSE WHEEL SCROLL EFFECT ===== */

.sections-page {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  background: #000;
}

/* Contenedor principal de secciones */
.sections-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

/* Wrapper de cada sección */
.section-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Secciones dentro del wrapper */
.section-wrapper > section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  border-radius: 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  padding-top: 80px; /* Espacio para el header */
  box-sizing: border-box;
}

/* ===== ESTADOS DE LAS SECCIONES ===== */

/* Sección actual - visible y fija */
.section-wrapper.current {
  z-index: 10;
}

.section-wrapper.current > section {
  transform: translateY(0);
  opacity: 1;
}

/* Secciones que están debajo de la actual - no visibles */
.section-wrapper.below {
  z-index: 1;
}

.section-wrapper.below > section {
  transform: translateY(0);
  opacity: 1;
}

/* Secciones que están arriba de la actual - fuera de pantalla */
.section-wrapper.above {
  z-index: 5;
}

.section-wrapper.above > section {
  transform: translateY(100%);
  opacity: 1;
}

/* ===== TRANSICIONES ===== */

/* Transición hacia abajo - sección que se está ocultando */
.section-wrapper.exiting-down {
  z-index: 9;
}

.section-wrapper.exiting-down > section {
  transform: translateY(0);
  opacity: 1;
  transition: none;
}

/* Transición hacia abajo - sección que está apareciendo */
.section-wrapper.entering-down {
  z-index: 11;
}

.section-wrapper.entering-down > section {
  transform: translateY(0);
  opacity: 1;
  animation: slideUpFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Transición hacia arriba - sección que se está ocultando */
.section-wrapper.exiting-up {
  z-index: 11;
}

.section-wrapper.exiting-up > section {
  transform: translateY(0);
  opacity: 1;
  animation: slideDownToBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Transición hacia arriba - sección que está apareciendo */
.section-wrapper.entering-up {
  z-index: 9;
}

.section-wrapper.entering-up > section {
  transform: translateY(0);
  opacity: 1;
  transition: none;
}

/* ===== ANIMACIONES ===== */

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDownToBottom {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

/* ===== INDICADOR DE SECCIÓN ===== */

.section-indicator {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.indicator-dot.active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.2);
}

.indicator-dot:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

/* ===== ESTADO DE TRANSICIÓN ===== */

.sections-page.transitioning {
  pointer-events: none;
}

.sections-page.transitioning .section-indicator {
  pointer-events: none;
}

/* ===== RESPONSIVE ===== */

@media (max-width: 768px) {
  .section-wrapper.entering-down > section {
    animation-duration: 0.6s;
  }

  .section-wrapper.exiting-up > section {
    animation-duration: 0.6s;
  }

  .section-wrapper > section {
    padding-top: 70px; /* Menos padding en tablets */
  }

  .section-indicator {
    right: 20px;
    gap: 10px;
  }

  .indicator-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .section-wrapper.entering-down > section {
    animation-duration: 0.5s;
  }

  .section-wrapper.exiting-up > section {
    animation-duration: 0.5s;
  }

  .section-wrapper > section {
    padding-top: 60px; /* Menos padding en móviles */
  }

  .section-indicator {
    right: 15px;
    gap: 8px;
  }

  .indicator-dot {
    width: 8px;
    height: 8px;
  }
}

/* ===== DISABLE SCROLL ===== */

html, body {
  overflow: hidden;
  height: 100vh;
  scroll-behavior: auto;
}
