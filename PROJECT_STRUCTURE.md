# Estructura del Proyecto - Greta Landing Page

## 📁 Estructura de Carpetas

```
src/
├── components/
│   ├── animations/          # Componentes de animación reutilizables
│   │   ├── ZoomEffect.jsx   # Efecto de zoom del rectángulo negro
│   │   ├── ZoomEffect.css
│   │   ├── CardStackEffect.jsx  # Efecto de tarjetas apiladas
│   │   └── CardStackEffect.css
│   ├── sections/            # Secciones específicas de la landing
│   │   ├── HeroSection.jsx  # Sección principal con logo Greta
│   │   ├── HeroSection.css
│   │   ├── ServicesSection.jsx  # Sección de servicios
│   │   └── ServicesSection.css
│   ├── ui/                  # Componentes de UI básicos (futuro)
│   └── layout/              # Componentes de layout (futuro)
├── pages/
│   └── Landing/
│       ├── LandingPage.jsx  # Página principal que combina todos los efectos
│       └── LandingPage.css
├── utils/
│   └── animations/
│       └── scrollUtils.js   # Utilidades para animaciones de scroll
├── styles/
│   └── globals/
│       ├── reset.css        # Reset CSS
│       └── variables.css    # Variables CSS globales
└── assets/                  # Imágenes, iconos, etc.
    ├── images/
    ├── icons/
    └── videos/
```

## 🎨 Efectos Implementados

### 1. **Efecto de Zoom (ZoomEffect.jsx)**
- **Descripción**: Rectángulo negro que comienza pequeño y se expande hasta llenar toda la pantalla
- **Trigger**: Scroll del usuario
- **Ubicación**: Componente que envuelve todo el contenido
- **Personalizable**: Velocidad de expansión, opacidad, punto de activación

### 2. **Efecto de Tarjetas Apiladas (CardStackEffect.jsx)**
- **Descripción**: Secciones que aparecen desde abajo como tarjetas que se apilan
- **Trigger**: Scroll del usuario
- **Ubicación**: Dentro del ZoomEffect
- **Personalizable**: Número de secciones, velocidad de aparición, timing

## 🚀 Cómo Agregar Nuevas Secciones

Para agregar una nueva sección a la landing page:

1. **Crear el componente de la sección**:
   ```jsx
   // src/components/sections/NuevaSeccion.jsx
   import React from 'react';
   import './NuevaSeccion.css';

   const NuevaSeccion = () => {
     return (
       <section className="nueva-seccion">
         {/* Contenido de la sección */}
       </section>
     );
   };

   export default NuevaSeccion;
   ```

2. **Agregar estilos**:
   ```css
   /* src/components/sections/NuevaSeccion.css */
   .nueva-seccion {
     width: 100%;
     min-height: 100vh;
     /* Estilos específicos */
   }
   ```

3. **Registrar en LandingPage.jsx**:
   ```jsx
   import NuevaSeccion from '../../components/sections/NuevaSeccion';

   const sections = [
     { id: 'hero', component: <HeroSection /> },
     { id: 'services', component: <ServicesSection /> },
     { id: 'nueva', component: <NuevaSeccion /> }, // ← Agregar aquí
   ];
   ```

## 🎯 Características Técnicas

### **Animaciones**
- Basadas en scroll del usuario
- Optimizadas con throttling para mejor performance
- Responsive design incluido
- Transiciones suaves con CSS cubic-bezier

### **Performance**
- Componentes optimizados con React hooks
- Event listeners con cleanup automático
- CSS optimizado para animaciones GPU-accelerated

### **Responsive**
- Mobile-first approach
- Breakpoints: 480px, 768px, 1024px+
- Animaciones adaptadas para dispositivos móviles

## 🛠️ Tecnologías Utilizadas

- **React 18** - Framework principal
- **Vite** - Build tool y dev server
- **CSS3** - Animaciones y estilos
- **Tailwind CSS** - Framework CSS utilitario
- **ESLint** - Linting de código

## 📱 Responsive Design

El proyecto está optimizado para:
- **Desktop**: 1024px+
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

## 🎨 Paleta de Colores

```css
--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--primary-color: #667eea;
--secondary-color: #764ba2;
--accent-color: #f093fb;
--text-primary: #2d3748;
--text-secondary: #718096;
```

## 🚀 Próximos Pasos

1. **Agregar más secciones** basadas en las capturas de Figma
2. **Implementar animaciones adicionales** (parallax, morphing, etc.)
3. **Optimizar performance** con lazy loading
4. **Agregar micro-interacciones** en botones y elementos
5. **Implementar modo oscuro** (opcional)

## 📝 Notas de Desarrollo

- Los componentes están diseñados para ser reutilizables
- Las animaciones son modulares y configurables
- El código está documentado para facilitar mantenimiento
- Se siguen las mejores prácticas de React y CSS
