import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Hook personalizado para manejar el efecto de scroll tipo mouse wheel
 * Cada sección se mantiene fija hasta que la siguiente la cubre completamente
 * Solo permite avanzar cuando la transición actual está completa
 */
const useMouseWheelScroll = (sectionsCount) => {
  const [currentSection, setCurrentSection] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [transitionDirection, setTransitionDirection] = useState(null); // 'up' | 'down'

  const wheelTimeoutRef = useRef(null);
  const transitionTimeoutRef = useRef(null);
  const lastWheelTime = useRef(0);
  const accumulatedDelta = useRef(0);

  // Refs para mantener valores actuales en el closure
  const currentSectionRef = useRef(currentSection);
  const isTransitioningRef = useRef(isTransitioning);

  // Actualizar refs cuando cambien los estados
  useEffect(() => {
    currentSectionRef.current = currentSection;
  }, [currentSection]);

  useEffect(() => {
    isTransitioningRef.current = isTransitioning;
  }, [isTransitioning]);
  
  // Configuración de sensibilidad
  const WHEEL_THRESHOLD = 50; // Umbral para detectar intención de scroll (reducido)
  const TRANSITION_DURATION = 800; // Duración de la transición en ms
  const WHEEL_COOLDOWN = 100; // Tiempo mínimo entre detecciones de wheel (aumentado)

  /**
   * Función para cambiar a la siguiente sección
   */
  const goToNextSection = useCallback(() => {
    console.log('goToNextSection called:', { currentSection, sectionsCount, isTransitioning });

    if (currentSection < sectionsCount - 1 && !isTransitioning) {
      console.log('Going to next section:', currentSection + 1);
      setIsTransitioning(true);
      setTransitionDirection('down');
      setCurrentSection(prev => prev + 1);

      // Resetear transición después de la duración
      transitionTimeoutRef.current = setTimeout(() => {
        console.log('Transition completed');
        setIsTransitioning(false);
        setTransitionDirection(null);
      }, TRANSITION_DURATION);
    } else {
      console.log('Cannot go to next section - at end or transitioning');
    }
  }, [currentSection, sectionsCount, isTransitioning]);

  /**
   * Función para cambiar a la sección anterior
   */
  const goToPreviousSection = useCallback(() => {
    console.log('goToPreviousSection called:', { currentSection, isTransitioning });

    if (currentSection > 0 && !isTransitioning) {
      console.log('Going to previous section:', currentSection - 1);
      setIsTransitioning(true);
      setTransitionDirection('up');
      setCurrentSection(prev => prev - 1);

      // Resetear transición después de la duración
      transitionTimeoutRef.current = setTimeout(() => {
        console.log('Transition completed');
        setIsTransitioning(false);
        setTransitionDirection(null);
      }, TRANSITION_DURATION);
    } else {
      console.log('Cannot go to previous section - at start or transitioning');
    }
  }, [currentSection, isTransitioning]);

  /**
   * Manejador de eventos de wheel
   */
  const handleWheel = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();

    console.log('Wheel event detected:', {
      deltaY: event.deltaY,
      currentSection,
      isTransitioning,
      accumulated: accumulatedDelta.current
    });

    const now = Date.now();

    // Cooldown para evitar múltiples triggers
    if (now - lastWheelTime.current < WHEEL_COOLDOWN) {
      console.log('Wheel cooldown active');
      return;
    }

    // Si estamos en transición, ignorar
    if (isTransitioning) {
      console.log('Transition in progress, ignoring wheel');
      return;
    }

    // Detectar dirección directamente sin acumulación para mayor responsividad
    const direction = event.deltaY > 0 ? 'down' : 'up';

    if (direction === 'down') {
      console.log('Attempting to go to next section');
      goToNextSection();
    } else {
      console.log('Attempting to go to previous section');
      goToPreviousSection();
    }

    lastWheelTime.current = now;
  }, [currentSection, isTransitioning, goToNextSection, goToPreviousSection]);

  /**
   * Manejador de eventos de teclado (flechas y Page Up/Down)
   */
  const handleKeyDown = useCallback((event) => {
    if (isTransitioning) return;
    
    switch (event.key) {
      case 'ArrowDown':
      case 'PageDown':
        event.preventDefault();
        goToNextSection();
        break;
      case 'ArrowUp':
      case 'PageUp':
        event.preventDefault();
        goToPreviousSection();
        break;
      default:
        break;
    }
  }, [isTransitioning, goToNextSection, goToPreviousSection]);

  useEffect(() => {
    console.log('Setting up wheel listeners - ONCE');

    const keyHandler = (event) => {
      if (isTransitioningRef.current) return;

      switch (event.key) {
        case 'ArrowDown':
        case 'PageDown':
          event.preventDefault();
          if (currentSectionRef.current < sectionsCount - 1) {
            setIsTransitioning(true);
            setTransitionDirection('down');
            setCurrentSection(currentSectionRef.current + 1);

            transitionTimeoutRef.current = setTimeout(() => {
              setIsTransitioning(false);
              setTransitionDirection(null);
            }, TRANSITION_DURATION);
          }
          break;
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          if (currentSectionRef.current > 0) {
            setIsTransitioning(true);
            setTransitionDirection('up');
            setCurrentSection(currentSectionRef.current - 1);

            transitionTimeoutRef.current = setTimeout(() => {
              setIsTransitioning(false);
              setTransitionDirection(null);
            }, TRANSITION_DURATION);
          }
          break;
        default:
          break;
      }
    };

    const wheelHandler = (event) => {
      // Detener inmediatamente la propagación para evitar conflictos
      event.preventDefault();
      event.stopImmediatePropagation();

      // Usar refs para obtener valores actuales
      const currentSectionValue = currentSectionRef.current;
      const isTransitioningValue = isTransitioningRef.current;

      console.log('Direct wheel event:', {
        deltaY: event.deltaY,
        currentSection: currentSectionValue,
        isTransitioning: isTransitioningValue,
        sectionsCount
      });

      const now = Date.now();

      // Cooldown para evitar múltiples triggers
      if (now - lastWheelTime.current < WHEEL_COOLDOWN) {
        console.log('Wheel cooldown active');
        return false;
      }

      // Si estamos en transición, ignorar
      if (isTransitioningValue) {
        console.log('Transition in progress, ignoring wheel');
        return false;
      }

      // Detectar dirección directamente
      const direction = event.deltaY > 0 ? 'down' : 'up';

      if (direction === 'down' && currentSectionValue < sectionsCount - 1) {
        console.log('Going down to section:', currentSectionValue + 1);
        setIsTransitioning(true);
        setTransitionDirection('down');
        setCurrentSection(currentSectionValue + 1);

        transitionTimeoutRef.current = setTimeout(() => {
          console.log('Transition completed');
          setIsTransitioning(false);
          setTransitionDirection(null);
        }, TRANSITION_DURATION);
      } else if (direction === 'up' && currentSectionValue > 0) {
        console.log('Going up to section:', currentSectionValue - 1);
        setIsTransitioning(true);
        setTransitionDirection('up');
        setCurrentSection(currentSectionValue - 1);

        transitionTimeoutRef.current = setTimeout(() => {
          console.log('Transition completed');
          setIsTransitioning(false);
          setTransitionDirection(null);
        }, TRANSITION_DURATION);
      } else {
        console.log('Cannot move:', { direction, currentSection: currentSectionValue, sectionsCount, isTransitioning: isTransitioningValue });
      }

      lastWheelTime.current = now;
      return false;
    };

    // Agregar listeners con máxima prioridad
    document.body.addEventListener('wheel', wheelHandler, { passive: false, capture: true });
    document.addEventListener('keydown', keyHandler, { capture: true });

    // También agregar al window como backup
    window.addEventListener('wheel', wheelHandler, { passive: false, capture: true });

    // Cleanup
    return () => {
      console.log('Cleaning up wheel listeners');
      document.body.removeEventListener('wheel', wheelHandler, { capture: true });
      document.removeEventListener('keydown', keyHandler, { capture: true });
      window.removeEventListener('wheel', wheelHandler, { capture: true });

      if (wheelTimeoutRef.current) {
        clearTimeout(wheelTimeoutRef.current);
      }
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, [sectionsCount]); // Solo dependencias estables

  /**
   * Función para obtener el estado de una sección específica
   * @param {number} index - Índice de la sección
   * @returns {string} - Estado de la sección
   */
  const getSectionState = useCallback((index) => {
    if (isTransitioning) {
      if (transitionDirection === 'down') {
        // Transición hacia abajo
        if (index === currentSection - 1) {
          return 'exiting-down'; // Sección que se está ocultando
        }
        if (index === currentSection) {
          return 'entering-down'; // Sección que está apareciendo
        }
      } else if (transitionDirection === 'up') {
        // Transición hacia arriba
        if (index === currentSection + 1) {
          return 'exiting-up'; // Sección que se está ocultando
        }
        if (index === currentSection) {
          return 'entering-up'; // Sección que está apareciendo
        }
      }
    }

    if (index === currentSection) {
      return 'current';
    }

    if (index < currentSection) {
      return 'below'; // Secciones que están debajo
    }

    return 'above'; // Secciones que están arriba (no visibles)
  }, [currentSection, isTransitioning, transitionDirection]);

  /**
   * Función para navegar directamente a una sección
   * @param {number} sectionIndex - Índice de la sección
   */
  const goToSection = useCallback((sectionIndex) => {
    if (sectionIndex >= 0 && sectionIndex < sectionsCount && !isTransitioning) {
      const direction = sectionIndex > currentSection ? 'down' : 'up';
      setIsTransitioning(true);
      setTransitionDirection(direction);
      setCurrentSection(sectionIndex);
      
      transitionTimeoutRef.current = setTimeout(() => {
        setIsTransitioning(false);
        setTransitionDirection(null);
      }, TRANSITION_DURATION);
    }
  }, [currentSection, sectionsCount, isTransitioning]);

  return {
    currentSection,
    isTransitioning,
    transitionDirection,
    getSectionState,
    goToSection,
    goToNextSection,
    goToPreviousSection
  };
};

export default useMouseWheelScroll;
