.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 20px 0;
  transition: all 0.3s ease;
}

.header-content {
  width: 100%;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Logo Styles */
.header-logo {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.header-logo:hover {
  transform: scale(1.05);
}

.greta-logo-img {
  height: 40px;
  width: auto;
  transition: opacity 0.3s ease;
}

/* CTA Button Styles */
.header-cta-button {
  background: linear-gradient(90deg, #B30FDC 0%, #156CFF 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(179, 15, 220, 0.3);
}

.header-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(179, 15, 220, 0.4);
}

/* Header Variants - Transparente */
.header.light {
  background: transparent;
  border-bottom: none;
}

.header.dark {
  background: transparent;
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 15px 0;
  }

  .header-content {
    padding: 0 20px;
  }

  .greta-logo-img {
    height: 30px;
  }

  .header-cta-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 15px;
  }

  .greta-logo-img {
    height: 25px;
  }

  .header-cta-button {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
}
