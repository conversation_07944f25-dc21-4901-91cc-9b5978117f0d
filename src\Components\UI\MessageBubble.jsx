import React from 'react';
import './MessageBubble.css';

/**
 * Componente de burbuja de mensaje estilo chat
 * @param {string} text - Texto del mensaje
 * @param {string} position - Posición de la cola: 'top-right', 'top-left', 'bottom-right', 'bottom-left'
 * @param {string} variant - <PERSON><PERSON>te de estilo: 'primary', 'secondary'
 * @param {string} className - Clases CSS adicionales
 * @param {object} style - Estilos inline adicionales
 */
const MessageBubble = ({ 
  text, 
  position = 'top-right', 
  variant = 'primary',
  className = '',
  style = {},
  ...props 
}) => {
  return (
    <div 
      className={`message-bubble message-bubble--${position} message-bubble--${variant} ${className}`}
      style={style}
      {...props}
    >
      <div className="message-bubble__content">
        {text}
      </div>
      <div className="message-bubble__tail"></div>
    </div>
  );
};

export default MessageBubble;
