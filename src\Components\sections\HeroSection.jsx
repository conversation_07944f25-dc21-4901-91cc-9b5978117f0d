import { useNavigate } from 'react-router-dom';
import './HeroSection.css';

/**
 * Sección Hero - La página inicial (fondo claro)
 * Diseño basado en Figma: Logo Greta en esquina superior izquierda,
 * botón púrpura en esquina superior derecha, contenido centrado
 */
const HeroSection = () => {
  const navigate = useNavigate();

  const handleEmpecemosClick = () => {
    navigate('/empecemos');
  };

  return (
    <section className="hero-section">
      <div className="hero-content">
        {/* Contenido principal centrado */}
        <div className="hero-main-content">
          <h1 className="hero-title">
            ¿Tienes un <span className="highlight">proyecto</span> por hacer?
          </h1>
          <p className="hero-subtitle">
            Investigamos para Desarrollar Soluciones
          </p>

          {/* Botón negro centrado */}
          <button className="cta-button-dark" onClick={handleEmpecemosClick}>
            Empecemos!
          </button>
        </div>

        {/* Líneas decorativas púrpuras */}
        <div className="decorative-elements">
          <div className="purple-line line-1"></div>
          <div className="purple-line line-2"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
