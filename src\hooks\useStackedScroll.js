import { useState, useEffect, useRef } from 'react';

/**
 * Hook personalizado para manejar el efecto de scroll apilado
 * Controla qué sección está activa basándose en la posición del scroll
 */
const useStackedScroll = (sectionsCount) => {
  const [activeSection, setActiveSection] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef(null);
  const lastScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      // Calcular qué sección debería estar activa
      // Usar un offset más pequeño para transiciones más precisas
      const sectionIndex = Math.floor((scrollY + windowHeight * 0.05) / windowHeight);
      const clampedIndex = Math.max(0, Math.min(sectionIndex, sectionsCount - 1));

      // Solo actualizar si cambió la sección
      if (clampedIndex !== activeSection) {
        setActiveSection(clampedIndex);
      }

      // Detectar si estamos haciendo scroll
      setIsScrolling(true);

      // Limpiar timeout anterior
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Establecer que dejamos de hacer scroll después de 100ms para mayor responsividad
      scrollTimeoutRef.current = setTimeout(() => {
        setIsScrolling(false);
      }, 100);

      lastScrollY.current = scrollY;
    };

    // Agregar listener de scroll
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Llamar una vez para establecer estado inicial
    handleScroll();

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [activeSection, sectionsCount]);

  /**
   * Función para obtener el estado de una sección específica
   * @param {number} index - Índice de la sección
   * @returns {string} - 'active', 'exiting', o ''
   */
  const getSectionState = (index) => {
    if (index === activeSection) {
      return 'active';
    }
    if (index <= activeSection) {
      return 'exiting';
    }
    return '';
  };

  /**
   * Función para navegar a una sección específica
   * @param {number} sectionIndex - Índice de la sección a la que navegar
   */
  const scrollToSection = (sectionIndex) => {
    const targetY = sectionIndex * window.innerHeight;
    window.scrollTo({
      top: targetY,
      behavior: 'smooth'
    });
  };

  return {
    activeSection,
    isScrolling,
    getSectionState,
    scrollToSection
  };
};

export default useStackedScroll;
