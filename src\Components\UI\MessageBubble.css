.message-bubble {
  position: relative;
  display: inline-block;
  max-width: 280px;
  z-index: 10;
}

.message-bubble__content {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(224, 224, 224, 0.3);
  border-radius: 18px;
  padding: 12px 18px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #156CFF;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

/* Variantes de color */
.message-bubble--primary .message-bubble__content {
  color: #156CFF;
  background: rgba(255, 255, 255, 0.85);
}

.message-bubble--secondary .message-bubble__content {
  color: #5B696E;
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(91, 105, 110, 0.2);
}

/* Cola/punta de la burbuja */
.message-bubble__tail {
  position: absolute;
  width: 0;
  height: 0;
  z-index: -1;
}

/* Posiciones de la cola */
/* Top Right - cola apunta hacia abajo y a la derecha */
.message-bubble--top-right .message-bubble__tail {
  bottom: -10px;
  right: 25px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 12px solid rgba(255, 255, 255, 0.85);
  transform: rotate(20deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
}

.message-bubble--top-right .message-bubble__tail::before {
  content: '';
  position: absolute;
  bottom: 1px;
  right: -9px;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 10px solid rgba(224, 224, 224, 0.3);
  transform: rotate(-20deg);
}

/* Top Left - cola apunta hacia abajo y a la izquierda */
.message-bubble--top-left .message-bubble__tail {
  bottom: -10px;
  left: 25px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 12px solid rgba(255, 255, 255, 0.85);
  transform: rotate(-20deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
}

/* Bottom Right - cola apunta hacia arriba y a la derecha */
.message-bubble--bottom-right .message-bubble__tail {
  top: -10px;
  right: 25px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 12px solid rgba(255, 255, 255, 0.85);
  transform: rotate(-20deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
}

/* Bottom Left - cola apunta hacia arriba y a la izquierda */
.message-bubble--bottom-left .message-bubble__tail {
  top: -10px;
  left: 25px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 12px solid rgba(255, 255, 255, 0.85);
  transform: rotate(20deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
}

/* Efectos hover */
.message-bubble:hover .message-bubble__content {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.92);
}

.message-bubble:hover .message-bubble__tail {
  transform: scale(1.05) rotate(20deg);
}

.message-bubble--top-left:hover .message-bubble__tail {
  transform: scale(1.05) rotate(-20deg);
}

.message-bubble--bottom-right:hover .message-bubble__tail {
  transform: scale(1.05) rotate(-20deg);
}

.message-bubble--bottom-left:hover .message-bubble__tail {
  transform: scale(1.05) rotate(20deg);
}

/* Animaciones de entrada */
.message-bubble {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
  animation: bubbleIn 0.6s ease forwards;
}

@keyframes bubbleIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  60% {
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 240px;
  }
  
  .message-bubble__content {
    padding: 12px 16px;
    font-size: 0.9rem;
    white-space: normal;
    text-align: center;
  }
  
  .message-bubble__tail {
    display: none;
  }
}
