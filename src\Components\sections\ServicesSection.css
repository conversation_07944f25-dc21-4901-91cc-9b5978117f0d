.services-section {
  min-height: 100vh;
  background: #ffffff;
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.services-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.services-container {
  max-width: 1200px;
  width: 100%;
}

/* Top Section - Header and Management Card side by side */
.services-top-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 60px;
  align-items: start;
}

/* Header */
.services-header {
  opacity: 0;
  animation: slideInUp 0.8s ease 0.3s forwards;
}

.services-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  text-align: left;
}

.title-underline {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #00E5FF 0%, #ADFF4D 100%);
  margin-bottom: 30px;
  border-radius: 2px;
}

.services-subtitle {
  font-size: 1.2rem;
  color: #555;
  max-width: 600px;
  line-height: 1.6;
  text-align: left;
}

/* Management Card */
.management-card {
  background: #f5f5f5;
  border-radius: 20px;
  padding: 30px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.6s forwards;
  transition: all 0.3s ease;
}

.management-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.management-content {
  flex: 1;
}

.management-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.management-button {
  background: linear-gradient(135deg, #B30FDC 0%, #156CFF 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.management-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(179, 15, 220, 0.3);
}

.management-icon {
  margin-left: 20px;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.service-card {
  border-radius: 30px;
  padding: 40px;
  min-height: 300px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Service Card Variants */
.service-card.dark {
  background: #2a2a2a;
  color: white;
}

.service-card.green {
  background: linear-gradient(135deg, #ADFF4D 0%, #00E5FF 100%);
  color: #333;
}

/* Service Content */
.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.service-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
  flex: 1;
}

.service-button {
  background: #ADFF4D;
  color: #333;
  border: none;
  padding: 12px 25px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.service-card.green .service-button {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.service-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Outsourcing Button Specific Gradient */
.service-button.outsourcing-button {
  background: linear-gradient(90deg, #ADFF4D 0%, #17FFE4 100%);
  color: #333;
}

.service-button.outsourcing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(173, 255, 77, 0.4);
}

/* Service Icon */
.service-icon {
  margin-left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .services-section {
    padding: 60px 20px;
  }

  .services-title {
    font-size: 2.5rem;
  }

  /* Stack top section vertically on mobile */
  .services-top-section {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .service-card {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
    min-height: auto;
  }

  .service-icon {
    margin-left: 0;
    margin-top: 20px;
  }

  .management-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .management-icon {
    margin-left: 0;
  }
}
