import { useState, useEffect } from 'react';
import PrismaLogo from '../../assets/logos/prisma_logo.png';
import ChatBotAvatar from '../../assets/IMG/chatbot_greta_avatar.png';
import ChatbotIcon from '../../assets/SVG/ChatbotIcon';
import DocumentIcon from '../../assets/SVG/DocumentIcon';
import DocumentChainIcon from '../../assets/SVG/DocumentChainIcon';
import CheckIcon from '../../assets/SVG/CheckIcon';
import ArrowIcon2 from '../../assets/SVG/ArrowIcon2';
import ArrowDownIcon from '../../assets/SVG/ArrowDownIcon';
import ArrowUpIcon from '../../assets/SVG/ArrowUpIcon';
import MessageBubble from '../UI/MessageBubble';
import './PrismaSection.css';

// Importar variable de entorno LINK_WSP
const LINK_WSP = import.meta.env.VITE_LINK_WSP;

/**
 * Sección de Prisma
 * Presenta el software para abogados con ChatBot Greta y funcionalidades
 */
const PrismaSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [expandedModule, setExpandedModule] = useState(0); // Primer módulo expandido por defecto
  const [isButtonHovered, setIsButtonHovered] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const toggleModule = (index) => {
    setExpandedModule(expandedModule === index ? -1 : index);
  };

  const features = [
    {
      title1: "Gestiona Tus",
      title2: "Contratos",
      description: "Organiza y administra todos tus contratos de manera eficiente"
    },
    {
      title1: "Haz seguimiento a tus",
      title2: "Procesos",
      description: "Monitorea el progreso de todos tus casos legales"
    },
    {
      title1: "Lleva orden en tus", 
      title2: "Registros", 
      description: "Mantén organizados todos tus documentos y expedientes"
    }
  ];

  const prismaModules = [
    {
      icon: ChatbotIcon,
      title: "ChatBot Greta",
      description: "Resuelve tus dudas legales, brinda información actualizada y detallada sobre tus trámites."
    },
    {
      icon: DocumentIcon,
      title: "Prisma Contratos",
      description: "Gestión completa de contratos y documentos legales"
    },
    {
      icon: DocumentChainIcon,
      title: "Prisma Procesos",
      description: "Seguimiento y administración de procesos judiciales"
    }
  ];

  return (
    <section className={`prisma-section ${isVisible ? 'visible' : ''}`}>
      <div className="prisma-container">
        {/* Header */}
        <div className="prisma-header">
          {/* ajustar tamaño */}
          <div className="prisma-logo-container">
            <img src={PrismaLogo} alt="Prisma Logo" className="prisma-logo w-[300px]" />
          </div>
          <h2 className="prisma-subtitle">
            El software para abogados <span className="highlight">hecho por abogados</span>
          </h2>
          
          {/* Features */}
          <div className="prisma-features">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="prisma-feature"
                style={{ '--delay': `${index * 0.2}s` }}
              >
                {/* Icono */}
                <CheckIcon className="feature-icon" />
                <span className="feature-text">{feature.title1}</span>
                <span className="feature-text highlight1">{feature.title2}</span>
              </div>
            ))}
          </div>
            {/* ajustar al tamaño del texto */}
          <button
            className="prisma-button"
            onMouseEnter={() => setIsButtonHovered(true)}
            onMouseLeave={() => setIsButtonHovered(false)}
          >
            Comunícate con nosotros
            <ArrowIcon2 color={isButtonHovered ? 'white' : '#156CFF'} />
          </button>
        </div>

        {/* Main Content */}
        <div className="prisma-content">
          {/* Left Side - Modules */}
          <div className="prisma-modules">
            {prismaModules.map((module, index) => (
              <div
                key={index}
                className={`prisma-module ${expandedModule === index ? 'expanded' : 'collapsed'}`}
                style={{ '--delay': `${(index + 1) * 0.3}s` }}
                onClick={() => toggleModule(index)}
              >
                <div className="module-header">
                  <span className="module-icon">
                    <module.icon color={expandedModule === index ? '#156CFF' : '#5B696E'} />
                  </span>
                  <h3 className="module-title">{module.title}</h3>
                  <span className="module-arrow">
                    {expandedModule === index ? <ArrowUpIcon /> : <ArrowDownIcon />}
                  </span>
                </div>
                {expandedModule === index && (
                  <p className="module-description">{module.description}</p>
                )}
              </div>
            ))}
          </div>

          {/* Right Side - ChatBot Avatar */}
          <div className="chatbot-container">
            <div className="chatbot-messages">
              <MessageBubble
                text="Tienes 20 contratos por firmar"
                position="top-right"
                variant="primary"
                className="message-top"
                style={{
                  position: 'absolute',
                  top: '-70px',
                  right: '-40px',
                  transform: 'rotate(3deg)',
                  animationDelay: '1.5s'
                }}
              />
              <MessageBubble
                text="¿Cuántos contratos tengo por firmar?"
                position="bottom-left"
                variant="primary"
                className="message-bottom"
                style={{
                  position: 'absolute',
                  top: '130px',
                  left: '-70px',
                  transform: 'rotate(-2deg)',
                  animationDelay: '1.8s'
                }}
              />
            </div>
            <img src={ChatBotAvatar} alt="ChatBot Avatar" className="chatbot-avatar" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default PrismaSection;
