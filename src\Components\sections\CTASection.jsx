import { useState, useEffect } from 'react';
import PetroTalLogo from '../../assets/logos/petrotal_logo.png';
import Ransa<PERSON><PERSON> from '../../assets/logos/ransa_logo.png';
import NexaLogo from '../../assets/logos/nexa_logo.png';
import AlfaparfLogo from '../../assets/logos/alfaparf_logo.png';
import StarIcon from '../../assets/SVG/StarIcon';
import ArrowIcon from '../../assets/SVG/ArrowIcon';
import './CTASection.css';

/**
 * Sección CTA Final
 * Call to action con pregunta sobre proyectos y logos de empresas
 */
const CTASection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const companies = [
    { name: "PetroTal", component: PetroTalLogo },
    { name: "<PERSON><PERSON><PERSON>", component: <PERSON>nsa<PERSON><PERSON> },
    { name: "<PERSON>par<PERSON>", component: AlfaparfLogo },
    { name: "Nexa", component: NexaLogo }
  ];

  // Duplicamos los logos para el efecto de carousel infinito suave
  const duplicatedCompanies = [...companies, ...companies];

  return (
    <section className={`cta-section ${isVisible ? 'visible' : ''} px-[0px]`}>
      <div className="w-full mb-[60px]" style={{ background: '#000000' }}>
        {/* Company Logos Carousel - Centered Container */}
        <div className="companies-carousel-container">
          <div className="companies-carousel">
            <div className="carousel-track">
              {duplicatedCompanies.map((company, index) => {
                const LogoComponent = company.component;
                return (
                  <div
                    key={`${company.name}-${index}`}
                    className="company-logo-carousel"
                  >
                    <img
                      src={LogoComponent}
                      alt={`${company.name} logo`}
                      className="logo-image"
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {/* Main CTA Content */}
      <div className="cta-container">
        <div className="cta-content">
          <div className="cta-card">
            <div className="cta-text">
              <div className="star-icon">
                <StarIcon width={40} height={40} />
              </div>
              <h2 className="cta-title">
                ¿Tienes un <span className="highlight">proyecto</span> por hacer?
              </h2>
              <p className="cta-subtitle">
                Somos un Laboratorio de Ideas
              </p>
            </div>

            <div className="cta-action">
              <button className="cta-button">
                Empecemos
                <div className="w-[0.9rem] transparent"></div>
                <ArrowIcon width={20} height={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Background Decorations */}
        <div className="background-decorations">
          <div className="decoration-circle decoration-1"></div>
          <div className="decoration-circle decoration-2"></div>
          <div className="decoration-circle decoration-3"></div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
