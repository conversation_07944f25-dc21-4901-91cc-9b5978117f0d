import React from 'react';

/**
 * Componente SVG del ícono de foco/bombilla
 * Usado en la sección hero para representar ideas innovadoras
 */
const FocoIcon = ({ width = 70, height = 86, className = "" }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 70 86"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M63 33.48C63 43.8 57.92 51.74 50.192 56.968C48.392 58.184 47.492 58.792 47.048 59.488C46.608 60.176 46.452 61.088 46.152 62.904L45.912 64.316C45.384 67.508 45.116 69.104 43.996 70.052C42.876 71 41.26 71 38.024 71H27.576C24.34 71 22.724 71 21.604 70.052C20.484 69.104 20.22 67.508 19.684 64.316L19.452 62.904C19.148 61.092 19 60.184 18.56 59.496C18.12 58.808 17.216 58.192 15.412 56.96C7.768 51.732 3 43.796 3 33.48C3 16.652 16.432 3.00002 33 3.00002C35.0155 2.99788 37.0259 3.20161 39 3.60802"
        stroke="url(#paint0_linear_77_221)"
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M41 71V75C41 78.772 41 80.656 39.828 81.828C38.656 83 36.772 83 33 83C29.228 83 27.344 83 26.172 81.828C25 80.656 25 78.772 25 75V71M53 3L54.032 5.788C55.384 9.444 56.06 11.272 57.392 12.604C58.728 13.94 60.556 14.616 64.212 15.968L67 17L64.212 18.032C60.556 19.384 58.728 20.06 57.396 21.392C56.06 22.728 55.384 24.556 54.032 28.212L53 31L51.968 28.212C50.616 24.556 49.94 22.728 48.608 21.396C47.272 20.06 45.444 19.384 41.788 18.032L39 17L41.788 15.968C45.444 14.616 47.272 13.94 48.604 12.608C49.94 11.272 50.616 9.444 51.968 5.788L53 3Z"
        stroke="url(#paint1_linear_77_221)"
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient id="paint0_linear_77_221" x1="33" y1="3" x2="33" y2="71" gradientUnits="userSpaceOnUse">
          <stop stopColor="#ADFF4D"/>
          <stop offset="1" stopColor="#17FFE4"/>
        </linearGradient>
        <linearGradient id="paint1_linear_77_221" x1="46" y1="3" x2="46" y2="83" gradientUnits="userSpaceOnUse">
          <stop stopColor="#ADFF4D"/>
          <stop offset="1" stopColor="#17FFE4"/>
        </linearGradient>
      </defs>
    </svg>
  );
};

export default FocoIcon;
